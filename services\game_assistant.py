"""
游戏助手服务模块
"""
from typing import List, Dict, Any, Optional
from models.llm_client import deepseek_client
from services.knowledge_base import knowledge_base_service
from config import Config

class GameAssistantService:
    """游戏助手服务类"""
    
    def __init__(self):
        """初始化游戏助手服务"""
        self.llm_client = deepseek_client
        self.knowledge_base = knowledge_base_service
        self.conversation_history = []
        self.max_history_length = 10
    
    def get_game_advice(
        self, 
        user_question: str, 
        game_type: str = "通用游戏",
        use_history: bool = True
    ) -> Dict[str, Any]:
        """
        获取游戏攻略建议
        
        Args:
            user_question: 用户问题
            game_type: 游戏类型
            use_history: 是否使用对话历史
            
        Returns:
            包含建议和相关信息的字典
        """
        try:
            # 1. 搜索相关知识
            relevant_knowledge = self.knowledge_base.search_knowledge(
                query=user_question,
                top_k=5,
                similarity_threshold=0.6
            )
            
            # 2. 构建上下文
            context = self._build_context(relevant_knowledge)
            
            # 3. 生成回答
            advice = self.llm_client.generate_game_advice(
                user_question=user_question,
                context=context,
                game_type=game_type
            )
            
            # 4. 更新对话历史
            if use_history:
                self._update_conversation_history(user_question, advice)
            
            # 5. 构建响应
            response = {
                "advice": advice,
                "relevant_sources": [
                    {
                        "content": item["document"][:200] + "...",
                        "similarity": round(item["similarity"], 3),
                        "source": item["metadata"].get("source", "unknown")
                    }
                    for item in relevant_knowledge
                ],
                "game_type": game_type,
                "question": user_question
            }
            
            return response
            
        except Exception as e:
            print(f"获取游戏建议失败: {e}")
            return {
                "advice": "抱歉，我遇到了一些技术问题，请稍后再试。",
                "relevant_sources": [],
                "game_type": game_type,
                "question": user_question,
                "error": str(e)
            }
    
    def _build_context(self, relevant_knowledge: List[Dict[str, Any]]) -> str:
        """
        构建上下文信息
        
        Args:
            relevant_knowledge: 相关知识列表
            
        Returns:
            格式化的上下文字符串
        """
        if not relevant_knowledge:
            return "暂无相关背景信息。"
        
        context_parts = []
        for i, item in enumerate(relevant_knowledge, 1):
            content = item["document"]
            similarity = item["similarity"]
            source = item["metadata"].get("source", "unknown")
            
            context_parts.append(
                f"参考资料{i} (相似度: {similarity:.2f}, 来源: {source}):\n{content}\n"
            )
        
        return "\n".join(context_parts)
    
    def _update_conversation_history(self, question: str, answer: str):
        """
        更新对话历史
        
        Args:
            question: 用户问题
            answer: 助手回答
        """
        self.conversation_history.append({
            "question": question,
            "answer": answer,
            "timestamp": self._get_current_timestamp()
        })
        
        # 保持历史记录长度
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        获取对话历史
        
        Returns:
            对话历史列表
        """
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_game_suggestions(self, game_type: str = None) -> List[str]:
        """
        获取游戏建议问题
        
        Args:
            game_type: 游戏类型
            
        Returns:
            建议问题列表
        """
        base_suggestions = [
            "这个游戏的基本玩法是什么？",
            "有什么新手入门技巧吗？",
            "如何快速升级？",
            "装备系统怎么玩？",
            "有什么隐藏任务或彩蛋吗？",
            "PVP对战有什么技巧？",
            "资源管理有什么建议？",
            "如何组建最强队伍？"
        ]
        
        if game_type:
            # 根据游戏类型定制建议
            type_specific = {
                "RPG": [
                    "角色属性怎么分配？",
                    "技能树怎么点？",
                    "副本攻略有哪些？"
                ],
                "策略": [
                    "建筑布局有什么技巧？",
                    "资源分配策略是什么？",
                    "军事单位搭配建议？"
                ],
                "射击": [
                    "瞄准技巧有哪些？",
                    "武器选择建议？",
                    "地图控制要点？"
                ],
                "MOBA": [
                    "英雄选择建议？",
                    "团战配合技巧？",
                    "经济发展策略？"
                ]
            }
            
            if game_type in type_specific:
                return base_suggestions + type_specific[game_type]
        
        return base_suggestions
    
    def analyze_question_intent(self, question: str) -> Dict[str, Any]:
        """
        分析问题意图
        
        Args:
            question: 用户问题
            
        Returns:
            意图分析结果
        """
        intent_keywords = {
            "攻略": ["攻略", "怎么打", "如何通关", "怎么过", "打法"],
            "装备": ["装备", "武器", "道具", "物品", "gear"],
            "技能": ["技能", "技巧", "能力", "skill", "技术"],
            "升级": ["升级", "经验", "等级", "level", "提升"],
            "任务": ["任务", "quest", "mission", "副本"],
            "PVP": ["PVP", "对战", "竞技", "PK", "战斗"],
            "资源": ["资源", "金币", "材料", "货币", "resource"],
            "新手": ["新手", "入门", "初学", "beginner", "新人"]
        }
        
        detected_intents = []
        for intent, keywords in intent_keywords.items():
            if any(keyword in question for keyword in keywords):
                detected_intents.append(intent)
        
        return {
            "intents": detected_intents,
            "primary_intent": detected_intents[0] if detected_intents else "通用咨询",
            "confidence": len(detected_intents) / len(intent_keywords)
        }
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def get_assistant_stats(self) -> Dict[str, Any]:
        """
        获取助手统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "conversation_count": len(self.conversation_history),
            "knowledge_base_stats": self.knowledge_base.get_knowledge_stats(),
            "max_history_length": self.max_history_length
        }

# 全局游戏助手服务实例
game_assistant_service = GameAssistantService()
