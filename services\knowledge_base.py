"""
知识库服务模块
"""
import os
import json
from typing import List, Dict, Any, Optional
from models.vector_store import vector_store
from utils.data_loader import data_loader
from config import Config

class KnowledgeBaseService:
    """知识库服务类"""
    
    def __init__(self):
        """初始化知识库服务"""
        self.vector_store = vector_store
        self.data_loader = data_loader
        self.knowledge_base_path = Config.KNOWLEDGE_BASE_PATH
        
        # 确保知识库目录存在
        os.makedirs(self.knowledge_base_path, exist_ok=True)
    
    def load_knowledge_from_modelscope(
        self, 
        dataset_id: str, 
        subset_name: str = None,
        force_reload: bool = False
    ) -> bool:
        """
        从魔搭平台加载知识库
        
        Args:
            dataset_id: 数据集ID
            subset_name: 子集名称
            force_reload: 是否强制重新加载
            
        Returns:
            是否成功加载
        """
        try:
            # 检查是否已经加载过
            cache_file = os.path.join(self.knowledge_base_path, f"{dataset_id.replace('/', '_')}.json")
            
            if os.path.exists(cache_file) and not force_reload:
                print(f"从缓存加载知识库: {cache_file}")
                return self._load_from_cache(cache_file)
            
            # 从魔搭平台加载数据
            print(f"正在从魔搭平台下载数据集: {dataset_id}")
            raw_data = self.data_loader.load_from_modelscope(dataset_id, subset_name)
            
            if not raw_data:
                print("未能加载到数据")
                return False
            
            # 处理数据
            processed_data = self.data_loader.process_game_data(raw_data)
            
            # 保存到缓存
            self.data_loader.save_processed_data(processed_data, cache_file)
            
            # 添加到向量数据库
            return self._add_to_vector_store(processed_data, dataset_id)
            
        except Exception as e:
            print(f"从魔搭平台加载知识库失败: {e}")
            return False
    
    def load_knowledge_from_local(self, directory_path: str = None) -> bool:
        """
        从本地文件加载知识库
        
        Args:
            directory_path: 本地目录路径
            
        Returns:
            是否成功加载
        """
        try:
            path = directory_path or self.knowledge_base_path
            
            # 加载本地文件
            raw_data = self.data_loader.load_from_local_files(path)
            
            if not raw_data:
                print("未找到本地数据文件")
                return False
            
            # 处理数据
            processed_data = self.data_loader.process_game_data(raw_data)
            
            # 添加到向量数据库
            return self._add_to_vector_store(processed_data, "local_files")
            
        except Exception as e:
            print(f"从本地加载知识库失败: {e}")
            return False
    
    def _load_from_cache(self, cache_file: str) -> bool:
        """从缓存文件加载数据"""
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                processed_data = json.load(f)
            
            return self._add_to_vector_store(processed_data, "cached_data")
            
        except Exception as e:
            print(f"从缓存加载失败: {e}")
            return False
    
    def _add_to_vector_store(self, processed_data: List[Dict[str, Any]], source: str) -> bool:
        """添加数据到向量数据库"""
        try:
            documents = []
            metadatas = []
            ids = []
            
            for i, item in enumerate(processed_data):
                documents.append(item['content'])
                
                metadata = item.get('metadata', {})
                metadata['source'] = source
                metadata['doc_id'] = f"{source}_{i}"
                metadatas.append(metadata)
                
                ids.append(f"{source}_{i}")
            
            # 批量添加到向量数据库
            success = self.vector_store.add_documents(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            if success:
                print(f"成功将 {len(documents)} 个文档添加到向量数据库")
            
            return success
            
        except Exception as e:
            print(f"添加到向量数据库失败: {e}")
            return False
    
    def search_knowledge(
        self,
        query: str,
        top_k: int = 5,
        similarity_threshold: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        搜索知识库
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值
            
        Returns:
            搜索结果列表
        """
        try:
            # 搜索相似文档
            results = self.vector_store.search_similar(query, n_results=top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result['similarity'] >= similarity_threshold
            ]
            
            return filtered_results
            
        except Exception as e:
            print(f"搜索知识库失败: {e}")
            return []
    
    def get_knowledge_stats(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            collection_info = self.vector_store.get_collection_info()
            
            # 获取本地文件信息
            local_files = []
            if os.path.exists(self.knowledge_base_path):
                for file in os.listdir(self.knowledge_base_path):
                    if file.endswith('.json'):
                        local_files.append(file)
            
            return {
                "vector_store": collection_info,
                "local_cache_files": local_files,
                "knowledge_base_path": self.knowledge_base_path
            }
            
        except Exception as e:
            print(f"获取知识库统计信息失败: {e}")
            return {}
    
    def clear_knowledge_base(self) -> bool:
        """
        清空知识库
        
        Returns:
            是否成功清空
        """
        try:
            # 删除向量数据库集合
            success = self.vector_store.delete_collection()
            
            # 重新创建集合
            if success:
                self.vector_store.__init__()
            
            return success
            
        except Exception as e:
            print(f"清空知识库失败: {e}")
            return False
    
    def add_custom_knowledge(
        self,
        content: str,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        添加自定义知识

        Args:
            content: 内容文本
            metadata: 元数据

        Returns:
            是否成功添加
        """
        try:
            if not metadata:
                metadata = {"source": "custom", "type": "manual"}

            # 生成唯一ID，包含更多随机性
            import time
            import random
            import hashlib

            # 基于内容和时间生成唯一ID
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
            timestamp = int(time.time() * 1000)  # 毫秒级时间戳
            random_num = random.randint(1000, 9999)
            doc_id = f"custom_{timestamp}_{random_num}_{content_hash}"

            success = self.vector_store.add_documents(
                documents=[content],
                metadatas=[metadata],
                ids=[doc_id]
            )

            return success

        except Exception as e:
            print(f"添加自定义知识失败: {e}")
            return False

# 全局知识库服务实例
knowledge_base_service = KnowledgeBaseService()
