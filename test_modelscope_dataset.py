"""
测试魔搭社区数据集加载
"""
from modelscope.msdatasets import MsDataset

def test_dataset_loading():
    """测试数据集加载"""
    dataset_id = "mushaoshuai/game_strategy"
    
    print(f"🔍 测试数据集: {dataset_id}")
    
    try:
        # 尝试直接加载数据集
        print("📥 尝试加载数据集...")
        ds = MsDataset.load(dataset_id)
        
        print(f"✅ 数据集加载成功！")
        print(f"📊 数据集信息:")
        print(f"   - 类型: {type(ds)}")
        
        # 尝试获取数据集的基本信息
        try:
            print(f"   - 长度: {len(ds)}")
        except:
            print("   - 长度: 无法获取")
        
        # 尝试查看前几条数据
        print("\n📝 数据样本:")
        try:
            for i, item in enumerate(ds):
                print(f"   样本 {i+1}: {item}")
                if i >= 2:  # 只显示前3条
                    break
        except Exception as e:
            print(f"   无法获取数据样本: {e}")
            
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 尝试其他可能的数据集ID
        alternative_ids = [
            "mushaoshuai/game_strategy",
            "mushaoshuai/game-strategy", 
            "mushaoshuai/gameStrategy",
            "game_strategy",
            "game-strategy"
        ]
        
        print(f"\n🔄 尝试其他可能的数据集ID:")
        for alt_id in alternative_ids:
            try:
                print(f"   尝试: {alt_id}")
                ds = MsDataset.load(alt_id)
                print(f"   ✅ 成功加载: {alt_id}")
                return alt_id
            except Exception as alt_e:
                print(f"   ❌ 失败: {alt_e}")
        
        return None

def search_datasets():
    """搜索相关数据集"""
    print("\n🔍 搜索游戏相关数据集...")
    
    try:
        # 这里可以添加搜索逻辑
        # ModelScope可能有搜索API
        pass
    except Exception as e:
        print(f"搜索失败: {e}")

if __name__ == "__main__":
    print("🎮 测试魔搭社区游戏攻略数据集\n")
    
    success_id = test_dataset_loading()
    
    if not success_id:
        search_datasets()
        print("\n💡 建议:")
        print("1. 检查数据集ID是否正确")
        print("2. 确认数据集是否公开可访问")
        print("3. 检查网络连接")
        print("4. 尝试在魔搭社区网站上搜索该数据集")
