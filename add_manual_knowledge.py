"""
手动添加游戏攻略知识到知识库
由于魔搭数据集可能存在格式问题，我们先手动添加一些高质量的游戏攻略内容
"""
from services.knowledge_base import knowledge_base_service

def add_comprehensive_game_knowledge():
    """添加更全面的游戏攻略知识"""
    print("=== 添加综合游戏攻略知识 ===\n")
    
    # 更全面的游戏攻略数据
    game_knowledge = [
        {
            "content": "《原神》新手完整攻略：1.优先完成主线任务获得冒险等级和原石；2.每日委托任务必做，提供稳定经验和摩拉；3.合理分配体力，优先刷取角色突破材料；4.四星角色重点培养：香菱、班尼特、行秋、菲谢尔；5.武器选择：优先提升主C武器等级；6.圣遗物前期不用刷，45级后再考虑；7.探索地图收集神瞳提升神像等级；8.合理规划原石，建议攒够180抽再抽卡。",
            "metadata": {"game": "原神", "type": "新手攻略", "category": "RPG", "difficulty": "入门"}
        },
        {
            "content": "《王者荣耀》射手位高阶攻略：1.对线期：补兵为主，避免无意义消耗，利用草丛卡视野；2.装备出装：攻速鞋→无尽战刃→影刃→破晓→贤者的庇护→复活甲；3.团战站位：保持安全距离，优先攻击前排，注意走A技巧；4.意识培养：观察小地图，预判敌方gank，及时支援团战；5.英雄推荐：后羿（新手）、马可波罗（进阶）、公孙离（高端）。",
            "metadata": {"game": "王者荣耀", "type": "位置攻略", "category": "MOBA", "difficulty": "进阶"}
        },
        {
            "content": "《英雄联盟》打野全面指南：1.刷野路线：红buff→小鸟→狼→蓝buff→蛤蟆→石甲虫，注意拉野技巧；2.gank时机：敌方无闪现、推线过深、己方有控制技能时；3.视野控制：河道蟹、龙坑、大龙坑放置真眼；4.装备选择：根据队伍需要选择输出型或坦克型装备；5.英雄推荐：盲僧（操作型）、赵信（简单粗暴）、格雷夫斯（刷野快）。",
            "metadata": {"game": "英雄联盟", "type": "位置攻略", "category": "MOBA", "difficulty": "中级"}
        },
        {
            "content": "《我的世界》建筑大师技巧：1.规划设计：先在纸上画草图，确定建筑风格和比例；2.材料搭配：石砖+橡木原木（中世纪风格），石英+玻璃（现代风格），圆石+木板（乡村风格）；3.细节装饰：使用楼梯、台阶、栅栏增加层次感；4.光影效果：合理放置光源，避免怪物生成；5.地形改造：利用地形特点，与自然环境融合；6.内饰设计：功能性与美观性并重。",
            "metadata": {"game": "我的世界", "type": "建筑攻略", "category": "沙盒", "difficulty": "进阶"}
        },
        {
            "content": "《和平精英》枪法训练完整教程：1.基础设置：四指操作，调整合适的灵敏度；2.压枪练习：训练场反复练习AKM、M416等主流武器；3.预瞄技巧：提前瞄准可能出现敌人的位置；4.身法走位：左右摆动、跳跃、下蹲组合使用；5.配件选择：AKM配补偿器+垂直握把，M416配消焰器+轻型握把；6.实战应用：先开镜再开火，控制射击节奏。",
            "metadata": {"game": "和平精英", "type": "技巧攻略", "category": "射击", "difficulty": "中级"}
        },
        {
            "content": "《炉石传说》卡组构筑进阶指南：1.法力值曲线：1费2张、2费4-6张、3费4-6张、4费2-4张、5费以上4-6张；2.核心卡牌：每个卡组需要有明确的获胜条件和核心combo；3.过牌机制：确保有足够的过牌手段，避免手牌枯竭；4.解牌配比：根据环境调整解牌数量，快攻环境多带AOE；5.职业特色：法师控制、猎人快攻、牧师治疗、战士武器。",
            "metadata": {"game": "炉石传说", "type": "卡组攻略", "category": "卡牌", "difficulty": "进阶"}
        },
        {
            "content": "《文明6》科技胜利完整路线：1.前期发展：优先研究畜牧业、采矿业，建立稳定的资源基础；2.中期规划：重点发展教育、工程学，建设校园区和工业区；3.科技路线：计算机→机器人学→核聚变→离子推进器；4.城市规划：首都专注科技，其他城市负责生产和资源；5.政策选择：理性主义、自由市场、太空竞赛；6.外交策略：避免战争，专注内政发展。",
            "metadata": {"game": "文明6", "type": "胜利攻略", "category": "策略", "difficulty": "高级"}
        },
        {
            "content": "《模拟人生4》建房装修终极指南：1.房屋规划：合理分配功能区域，注意动线设计；2.作弊码使用：bb.moveobjects on允许自由放置物品；3.装修技巧：使用半墙、栅栏创造层次感，巧用平台工具；4.色彩搭配：选择2-3种主色调，避免过于花哨；5.家具选择：功能性优先，美观性其次；6.花园设计：合理搭配植物，创造自然氛围；7.MOD推荐：MCCC、UI扩展、更多特征。",
            "metadata": {"game": "模拟人生4", "type": "建造攻略", "category": "模拟", "difficulty": "中级"}
        },
        {
            "content": "《DOTA2》新手入门完整教程：1.基础操作：学会补刀、反补、拉野、控符；2.英雄选择：推荐新手英雄：巫医、复仇之魂、龙骑士、冥魂大帝；3.装备理解：了解属性装备、技能装备、消耗品的作用；4.地图意识：观察小地图，注意敌方英雄位置；5.团战配合：保护核心，集火秒杀；6.经济发展：合理分配资源，避免抢队友经济；7.版本理解：关注版本更新，适应meta变化。",
            "metadata": {"game": "DOTA2", "type": "新手攻略", "category": "MOBA", "difficulty": "入门"}
        },
        {
            "content": "《CS:GO》竞技模式进阶攻略：1.枪法训练：每日aim训练，练习甩枪、定位、压枪；2.地图理解：熟悉常见地图的点位、烟雾弹点、闪光弹配合；3.经济管理：合理购买装备，了解强起、eco、全甲全枪的时机；4.团队配合：学会报点、配合队友、执行战术；5.心态调整：保持冷静，不要因为失误影响后续发挥；6.装备选择：AK47/M4A4主武器，格洛克/USP副武器。",
            "metadata": {"game": "CS:GO", "type": "竞技攻略", "category": "射击", "difficulty": "高级"}
        }
    ]
    
    # 添加知识到知识库
    success_count = 0
    for i, knowledge in enumerate(game_knowledge):
        try:
            success = knowledge_base_service.add_custom_knowledge(
                content=knowledge["content"],
                metadata=knowledge["metadata"]
            )
            if success:
                success_count += 1
                print(f"✅ 已添加: {knowledge['metadata']['game']} - {knowledge['metadata']['type']}")
            else:
                print(f"❌ 添加失败: {knowledge['metadata']['game']} - {knowledge['metadata']['type']}")
        except Exception as e:
            print(f"❌ 添加出错: {e}")
    
    print(f"\n📊 添加完成统计:")
    print(f"- 总计: {len(game_knowledge)} 条知识")
    print(f"- 成功: {success_count} 条")
    print(f"- 失败: {len(game_knowledge) - success_count} 条")
    
    # 获取最新的知识库统计
    stats = knowledge_base_service.get_knowledge_stats()
    print(f"\n📈 当前知识库状态:")
    print(f"- 总文档数: {stats['vector_store']['count']}")
    print(f"- 存储位置: {stats['vector_store']['persist_directory']}")
    
    return success_count > 0

def test_new_knowledge():
    """测试新添加的知识"""
    print("\n🔍 测试新添加的知识...")
    
    test_queries = [
        "原神新手攻略",
        "王者荣耀射手怎么玩",
        "英雄联盟打野路线",
        "我的世界建筑技巧",
        "和平精英枪法练习",
        "炉石传说卡组构筑",
        "文明6科技胜利",
        "模拟人生4建房",
        "DOTA2新手教程",
        "CS:GO竞技技巧"
    ]
    
    for query in test_queries:
        print(f"\n搜索: {query}")
        results = knowledge_base_service.search_knowledge(query, top_k=2, similarity_threshold=0.5)
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"  结果{i}: {result['document'][:80]}...")
                print(f"  相似度: {result['similarity']:.3f}")
                print(f"  游戏: {result['metadata'].get('game', '未知')}")
        else:
            print("  未找到相关结果")

def main():
    """主函数"""
    print("开始添加综合游戏攻略知识...\n")
    
    success = add_comprehensive_game_knowledge()
    
    if success:
        test_new_knowledge()
        print("\n🎉 知识库扩展完成！")
        print("现在您可以在Web界面中询问更多游戏相关问题了。")
    else:
        print("\n❌ 知识添加失败，请检查系统状态。")

if __name__ == "__main__":
    main()
