// GameAI 智能游戏攻略助手前端JavaScript

class GameAssistant {
    constructor() {
        this.isLoading = false;
        this.startTime = Date.now();
        this.questionCount = parseInt(localStorage.getItem('questionCount') || '0');
        this.todayCount = parseInt(localStorage.getItem('todayCount') || '0');
        this.currentTheme = localStorage.getItem('theme') || 'default';
        this.init();
    }

    init() {
        // 初始化事件监听器
        this.setupEventListeners();

        // 加载建议问题
        this.loadSuggestions();

        // 初始化统计信息
        this.initStats();

        // 启动在线时长计时器
        this.startOnlineTimer();

        // 聚焦输入框
        document.getElementById('userInput').focus();

        // 应用主题
        this.applyTheme();
    }

    setupEventListeners() {
        // 游戏类型变化时重新加载建议
        document.getElementById('gameType').addEventListener('change', () => {
            this.loadSuggestions();
        });

        // 输入框焦点事件
        const userInput = document.getElementById('userInput');
        userInput.addEventListener('focus', () => {
            userInput.parentElement.style.transform = 'scale(1.02)';
        });

        userInput.addEventListener('blur', () => {
            userInput.parentElement.style.transform = 'scale(1)';
        });

        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseOnlineTimer();
            } else {
                this.resumeOnlineTimer();
            }
        });
    }

    // 发送消息
    async sendMessage() {
        if (this.isLoading) return;

        const input = document.getElementById('userInput');
        const question = input.value.trim();

        if (!question) {
            this.showAlert('请输入问题', 'warning');
            return;
        }

        // 更新统计
        this.updateQuestionCount();

        // 显示用户消息
        this.addMessage(question, 'user');
        
        // 清空输入框
        input.value = '';
        
        // 显示加载状态
        this.isLoading = true;
        const loadingMessage = this.addMessage('正在思考中...', 'assistant', true);
        
        try {
            const gameType = document.getElementById('gameType').value;
            
            const response = await fetch('/api/ask', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: question,
                    game_type: gameType
                })
            });

            const data = await response.json();
            
            // 移除加载消息
            loadingMessage.remove();
            
            if (data.success) {
                // 显示助手回复
                this.addMessage(data.data.advice, 'assistant');
                
                // 显示相关来源（如果有）
                if (data.data.relevant_sources && data.data.relevant_sources.length > 0) {
                    this.addSourcesMessage(data.data.relevant_sources);
                }
            } else {
                this.addMessage(`抱歉，出现了错误：${data.error}`, 'assistant');
            }
            
        } catch (error) {
            loadingMessage.remove();
            this.addMessage('抱歉，网络连接出现问题，请稍后再试。', 'assistant');
            console.error('Error:', error);
        } finally {
            this.isLoading = false;
        }
    }

    // 格式化文本，改善排版
    formatText(text) {
        if (!text) return '';

        // 处理各种格式化需求
        let formattedText = text
            // 处理数字列表（1. 2. 3.）
            .replace(/(\d+\.\s*\*\*[^*]+\*\*)/g, '<br><br>$1')
            .replace(/(\d+\.\s*[^1-9\n]*?)(?=\d+\.|$)/g, '<br><br>$1')

            // 处理项目符号列表（✅ ❌ 等）
            .replace(/(✅|❌|🎮|📊|🔍|⚠️|💡|🎯|📋|🚀)\s*/g, '<br><br>$1 ')

            // 处理Markdown标题（### ## #）
            .replace(/###\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
            .replace(/##\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')
            .replace(/#\s*([^\n]+)/g, '<br><br><strong>$1</strong><br>')

            // 处理粗体文本（**text**）
            .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')

            // 处理问号后的换行
            .replace(/([？?])\s*([1-9]\.)/g, '$1<br><br>$2')

            // 处理冒号后的换行（如果后面跟着列表）
            .replace(/([：:])\s*(✅|❌|[1-9]\.)/g, '$1<br><br>$2')

            // 处理感叹号和表情符号后的换行
            .replace(/([！!😊🎮])\s*([A-Z]|[1-9]\.|✅|❌)/g, '$1<br><br>$2')

            // 清理多余的换行
            .replace(/(<br>\s*){3,}/g, '<br><br>')
            .replace(/^(<br>\s*)+/, '')
            .replace(/(<br>\s*)+$/, '');

        return formattedText;
    }

    // 添加消息到聊天区域
    addMessage(text, sender, isLoading = false) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const icon = sender === 'user' ? 'bi-person-fill' : 'bi-robot';
        const loadingHtml = isLoading ? '<span class="loading"></span>' : '';

        // 对助手回复进行格式化
        const formattedText = sender === 'assistant' && !isLoading ?
            this.formatText(text) : text;

        messageDiv.innerHTML = `
            <div class="message-content">
                <i class="bi ${icon}"></i>
                <div class="message-text">
                    ${loadingHtml}${formattedText}
                </div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        return messageDiv;
    }

    // 添加相关来源信息
    addSourcesMessage(sources) {
        const messagesContainer = document.getElementById('chatMessages');
        const sourceDiv = document.createElement('div');
        sourceDiv.className = 'message assistant-message';
        
        let sourcesHtml = '<div class="sources-section">';
        sourcesHtml += '<div class="sources-title"><i class="bi bi-book"></i> 相关参考资料：</div>';
        
        sources.forEach((source, index) => {
            sourcesHtml += `
                <div class="source-item">
                    <div>${source.content}</div>
                    <div class="source-meta">
                        相似度: ${(source.similarity * 100).toFixed(1)}% | 
                        来源: ${source.source}
                    </div>
                </div>
            `;
        });
        
        sourcesHtml += '</div>';
        
        sourceDiv.innerHTML = `
            <div class="message-content">
                <i class="bi bi-robot"></i>
                <div class="message-text">
                    ${sourcesHtml}
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(sourceDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 加载建议问题
    async loadSuggestions() {
        try {
            const gameType = document.getElementById('gameType').value;
            const response = await fetch(`/api/suggestions?game_type=${encodeURIComponent(gameType)}`);
            const data = await response.json();
            
            if (data.success) {
                this.displaySuggestions(data.data);
            }
        } catch (error) {
            console.error('加载建议失败:', error);
        }
    }

    // 显示建议问题
    displaySuggestions(suggestions) {
        const container = document.getElementById('suggestions');
        container.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = suggestion;
            item.onclick = () => this.selectSuggestion(suggestion);
            container.appendChild(item);
        });
    }

    // 选择建议问题
    selectSuggestion(suggestion) {
        document.getElementById('userInput').value = suggestion;
        document.getElementById('userInput').focus();
    }

    // 处理键盘事件
    handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        }
    }

    // 显示提示信息
    showAlert(message, type = 'info') {
        // 创建提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);';
        alertDiv.innerHTML = `
            <strong>${this.getAlertIcon(type)}</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    // 获取提示图标
    getAlertIcon(type) {
        const icons = {
            'success': '✅',
            'warning': '⚠️',
            'danger': '❌',
            'info': 'ℹ️'
        };
        return icons[type] || 'ℹ️';
    }

    // 统计功能
    initStats() {
        this.updateStatsDisplay();
    }

    updateQuestionCount() {
        this.questionCount++;
        this.todayCount++;
        localStorage.setItem('questionCount', this.questionCount.toString());
        localStorage.setItem('todayCount', this.todayCount.toString());
        this.updateStatsDisplay();
    }

    updateStatsDisplay() {
        const totalElement = document.getElementById('totalCount');
        const todayElement = document.getElementById('todayCount');

        if (totalElement) totalElement.textContent = this.questionCount;
        if (todayElement) todayElement.textContent = this.todayCount;
    }

    // 在线时长功能
    startOnlineTimer() {
        this.onlineTimer = setInterval(() => {
            this.updateOnlineTime();
        }, 1000);
    }

    pauseOnlineTimer() {
        if (this.onlineTimer) {
            clearInterval(this.onlineTimer);
        }
    }

    resumeOnlineTimer() {
        this.startOnlineTimer();
    }

    updateOnlineTime() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const timeElement = document.getElementById('onlineTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // 主题切换功能
    applyTheme() {
        document.body.className = `theme-${this.currentTheme}`;
    }

    toggleTheme() {
        const themes = ['default', 'dark', 'gaming', 'neon'];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        this.currentTheme = themes[nextIndex];

        localStorage.setItem('theme', this.currentTheme);
        this.applyTheme();
        this.showAlert(`已切换到 ${this.getThemeName(this.currentTheme)} 主题`, 'success');
    }

    getThemeName(theme) {
        const names = {
            'default': '默认',
            'dark': '暗黑',
            'gaming': '游戏',
            'neon': '霓虹'
        };
        return names[theme] || '默认';
    }

    // 导出对话功能
    exportChat() {
        const messages = document.querySelectorAll('.message');
        let chatContent = `GameAI 对话记录\n生成时间: ${new Date().toLocaleString()}\n\n`;

        messages.forEach((message, index) => {
            const isUser = message.classList.contains('user-message');
            const text = message.querySelector('.message-text').textContent;
            const role = isUser ? '用户' : 'AI助手';
            chatContent += `${index + 1}. ${role}: ${text}\n\n`;
        });

        const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `GameAI对话记录_${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showAlert('对话记录已导出', 'success');
    }
}

// 全局函数
let gameAssistant;

document.addEventListener('DOMContentLoaded', function() {
    gameAssistant = new GameAssistant();
});

function sendMessage() {
    gameAssistant.sendMessage();
}

function handleKeyPress(event) {
    gameAssistant.handleKeyPress(event);
}

// 快捷问题功能
function askQuickQuestion(question) {
    document.getElementById('userInput').value = question;
    gameAssistant.sendMessage();
}

// 清空历史
function clearHistory() {
    if (confirm('确定要清空所有对话历史吗？')) {
        const chatMessages = document.getElementById('chatMessages');
        // 保留欢迎消息
        const welcomeMessage = chatMessages.querySelector('.assistant-message');
        chatMessages.innerHTML = '';
        if (welcomeMessage) {
            chatMessages.appendChild(welcomeMessage);
        }
        gameAssistant.showAlert('对话历史已清空', 'success');
    }
}

// 导出对话
function exportChat() {
    gameAssistant.exportChat();
}

// 切换主题
function toggleTheme() {
    gameAssistant.toggleTheme();
}

// 清空历史记录
async function clearHistory() {
    if (!confirm('确定要清空对话历史吗？')) {
        return;
    }

    try {
        const response = await fetch('/api/clear_history', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            gameAssistant.showAlert('对话历史已清空', 'success');
            // 清空聊天界面（保留欢迎消息）
            const messagesContainer = document.getElementById('chatMessages');
            const welcomeMessage = messagesContainer.firstElementChild;
            messagesContainer.innerHTML = '';
            messagesContainer.appendChild(welcomeMessage);
        } else {
            gameAssistant.showAlert(`清空失败：${data.error}`, 'danger');
        }

    } catch (error) {
        gameAssistant.showAlert('网络错误，请稍后再试', 'danger');
        console.error('Error:', error);
    }
}

// 辅助函数：显示加载遮罩
function showLoadingOverlay(message = '加载中...') {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">${message}</div>
        </div>
    `;
    document.body.appendChild(overlay);
    return overlay;
}

// 辅助函数：隐藏加载遮罩
function hideLoadingOverlay(overlay) {
    if (overlay && overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
    }
}

// 辅助函数：确保模态框完全关闭
function closeModal(modalId) {
    const modalElement = document.getElementById(modalId);
    const modalInstance = bootstrap.Modal.getInstance(modalElement);

    if (modalInstance) {
        modalInstance.hide();
    }

    // 确保移除所有模态框相关的类和遮罩
    setTimeout(() => {
        // 移除body上的modal-open类
        document.body.classList.remove('modal-open');

        // 移除所有遮罩层
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });

        // 恢复body的样式
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }, 300);
}

// 页面加载完成后的清理工作
document.addEventListener('DOMContentLoaded', function() {
    // 确保页面加载时没有残留的模态框遮罩
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop);
        }
    });

    // 确保body样式正常
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
});
