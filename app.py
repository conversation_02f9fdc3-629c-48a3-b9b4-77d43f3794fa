"""
Flask主应用文件
"""
from flask import Flask, render_template, request, jsonify, session
import os
from config import config
from services.game_assistant import game_assistant_service

def create_app(config_name='default'):
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')

    @app.route('/test')
    def test_formatting():
        """格式化测试页面"""
        with open('test_formatting.html', 'r', encoding='utf-8') as f:
            return f.read()
    
    @app.route('/api/ask', methods=['POST'])
    def ask_question():
        """处理用户问题"""
        try:
            data = request.get_json()
            question = data.get('question', '').strip()
            game_type = data.get('game_type', '通用游戏')
            
            if not question:
                return jsonify({
                    'success': False,
                    'error': '问题不能为空'
                }), 400
            
            # 获取游戏建议
            response = game_assistant_service.get_game_advice(
                user_question=question,
                game_type=game_type
            )
            
            return jsonify({
                'success': True,
                'data': response
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'处理请求时发生错误: {str(e)}'
            }), 500
    
    @app.route('/api/suggestions')
    def get_suggestions():
        """获取建议问题"""
        try:
            game_type = request.args.get('game_type')
            suggestions = game_assistant_service.get_game_suggestions(game_type)
            
            return jsonify({
                'success': True,
                'data': suggestions
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'获取建议失败: {str(e)}'
            }), 500
    
    @app.route('/api/history')
    def get_history():
        """获取对话历史"""
        try:
            history = game_assistant_service.get_conversation_history()
            
            return jsonify({
                'success': True,
                'data': history
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'获取历史失败: {str(e)}'
            }), 500
    
    @app.route('/api/clear_history', methods=['POST'])
    def clear_history():
        """清空对话历史"""
        try:
            game_assistant_service.clear_conversation_history()
            
            return jsonify({
                'success': True,
                'message': '对话历史已清空'
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'清空历史失败: {str(e)}'
            }), 500
    


    
    @app.route('/api/assistant/stats')
    def get_assistant_stats():
        """获取助手统计信息"""
        try:
            stats = game_assistant_service.get_assistant_stats()
            
            return jsonify({
                'success': True,
                'data': stats
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'获取助手统计信息失败: {str(e)}'
            }), 500
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        return jsonify({
            'success': False,
            'error': '页面未找到'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
