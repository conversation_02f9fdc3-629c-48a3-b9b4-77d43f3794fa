"""
DeepSeek API客户端模块
"""
import openai
import re
from typing import List, Dict, Any, Optional
from config import Config

class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self, api_key: str = None, base_url: str = None):
        """
        初始化DeepSeek客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
        """
        self.api_key = api_key or Config.DEEPSEEK_API_KEY
        self.base_url = base_url or Config.DEEPSEEK_BASE_URL
        self.model = Config.DEEPSEEK_MODEL
        
        # 配置OpenAI客户端以使用DeepSeek API
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        temperature: float = None,
        max_tokens: int = None,
        stream: bool = False
    ) -> str:
        """
        调用聊天完成API
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式输出
            
        Returns:
            生成的回复文本
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature or Config.TEMPERATURE,
                max_tokens=max_tokens or Config.MAX_TOKENS,
                stream=stream
            )
            
            if stream:
                return response
            else:
                return response.choices[0].message.content
                
        except Exception as e:
            print(f"DeepSeek API调用错误: {e}")
            return "抱歉，我遇到了一些技术问题，请稍后再试。"
    
    def generate_game_advice(
        self,
        user_question: str,
        context: str = "",
        game_type: str = "通用游戏"
    ) -> str:
        """
        生成游戏攻略建议

        Args:
            user_question: 用户问题
            context: 相关上下文信息
            game_type: 游戏类型

        Returns:
            游戏攻略建议
        """
        system_prompt = f"""你是一个专业的{game_type}攻略助手。你的任务是根据用户的问题和提供的相关信息，给出详细、准确、实用的游戏攻略建议。

请遵循以下格式要求：
1. 使用清晰的段落结构，每个要点之间空行
2. 使用数字列表（1. 2. 3.）或符号列表（✅ ❌）来组织内容
3. 重要信息用**粗体**标记
4. 每个段落不要太长，保持可读性
5. 如果有步骤说明，请分行列出

内容要求：
1. 回答要详细具体，包含具体的操作步骤
2. 如果有多种解决方案，请列出并说明各自的优缺点
3. 提供相关的游戏技巧和注意事项
4. 语言要通俗易懂，适合游戏玩家阅读
5. 如果信息不足，请说明需要更多具体信息

相关背景信息：
{context}
"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_question}
        ]

        response = self.chat_completion(messages)

        # 后处理：确保格式化
        return self._format_response(response)
    
    def summarize_knowledge(self, text: str, max_length: int = 500) -> str:
        """
        总结知识库内容
        
        Args:
            text: 要总结的文本
            max_length: 最大长度
            
        Returns:
            总结后的文本
        """
        messages = [
            {
                "role": "system", 
                "content": f"请将以下游戏攻略内容总结为不超过{max_length}字的精炼版本，保留关键信息和操作步骤："
            },
            {"role": "user", "content": text}
        ]
        
        return self.chat_completion(messages, max_tokens=max_length)

    def _format_response(self, response: str) -> str:
        """
        格式化AI回复，改善排版

        Args:
            response: 原始回复文本

        Returns:
            格式化后的文本
        """
        if not response:
            return response

        # 基本格式化处理
        formatted = response.strip()

        # 确保数字列表前有换行
        formatted = re.sub(r'([。！？])\s*(\d+\.)', r'\1\n\n\2', formatted)

        # 确保符号列表前有换行
        formatted = re.sub(r'([。！？])\s*(✅|❌|🎮|📊)', r'\1\n\n\2', formatted)

        # 确保标题前有换行
        formatted = re.sub(r'([。！？])\s*(###?\s*[^。！？\n]+)', r'\1\n\n\2', formatted)

        # 确保问号后的新段落有换行
        formatted = re.sub(r'([？?])\s*([A-Z]|[一-龥])', r'\1\n\n\2', formatted)

        # 清理多余的空行
        formatted = re.sub(r'\n{3,}', '\n\n', formatted)

        return formatted

# 全局客户端实例
deepseek_client = DeepSeekClient()
