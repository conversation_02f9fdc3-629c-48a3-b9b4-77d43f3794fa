<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 GameAI - 智能游戏攻略助手</title>
    <meta name="description" content="基于AI的智能游戏攻略助手，支持多种游戏类型的专业问答和攻略推荐">
    <meta name="keywords" content="游戏攻略,AI助手,游戏问答,RPG,MOBA,策略游戏">

    <!-- 外部资源 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h4><i class="bi bi-controller"></i> GameAI 助手</h4>
                    <p class="mb-0" style="font-size: 0.9rem; opacity: 0.9;">智能游戏攻略专家</p>
                </div>

                <!-- 游戏类型选择 -->
                <div class="mb-4">
                    <label for="gameType" class="form-label">🎯 游戏类型</label>
                    <select class="form-select" id="gameType">
                        <option value="通用游戏">🎮 通用游戏</option>
                        <option value="RPG">⚔️ RPG角色扮演</option>
                        <option value="策略">🧠 策略游戏</option>
                        <option value="射击">🔫 射击游戏</option>
                        <option value="MOBA">🏆 MOBA游戏</option>
                        <option value="卡牌">🃏 卡牌游戏</option>
                        <option value="模拟">🏗️ 模拟游戏</option>
                        <option value="竞速">🏎️ 竞速游戏</option>
                        <option value="动作">🥊 动作游戏</option>
                    </select>
                </div>

                <!-- 快捷功能 -->
                <div class="mb-4">
                    <h6>⚡ 快捷功能</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="askQuickQuestion('新手入门指南')">
                            <i class="bi bi-book"></i> 新手指南
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="askQuickQuestion('高级攻略技巧')">
                            <i class="bi bi-star"></i> 高级技巧
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="askQuickQuestion('装备推荐')">
                            <i class="bi bi-shield"></i> 装备推荐
                        </button>
                    </div>
                </div>

                <!-- 建议问题 -->
                <div class="mb-4">
                    <h6>💡 建议问题</h6>
                    <div id="suggestions" class="suggestions-list">
                        <!-- 动态加载建议问题 -->
                    </div>
                </div>

                <!-- 功能管理 -->
                <div class="mb-3">
                    <h6>🛠️ 功能管理</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-warning btn-sm" onclick="clearHistory()">
                            <i class="bi bi-trash"></i> 清空历史
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="exportChat()">
                            <i class="bi bi-download"></i> 导出对话
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="toggleTheme()">
                            <i class="bi bi-palette"></i> 切换主题
                        </button>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="mb-3">
                    <h6>📊 使用统计</h6>
                    <div class="stats-info">
                        <small class="text-muted">
                            <div>今日咨询: <span id="todayCount">0</span> 次</div>
                            <div>总计咨询: <span id="totalCount">0</span> 次</div>
                            <div>在线时长: <span id="onlineTime">00:00</span></div>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- 主聊天区域 -->
            <div class="col-md-9 main-content">
                <div class="chat-container">
                    <div class="chat-header">
                        <h5><i class="bi bi-chat-dots"></i> 智能游戏攻略咨询</h5>
                        <div class="header-status">
                            <span class="status-indicator online"></span>
                            <small>AI助手在线</small>
                        </div>
                    </div>

                    <div class="chat-messages" id="chatMessages">
                        <div class="message assistant-message">
                            <div class="message-content">
                                <i class="bi bi-robot"></i>
                                <div class="message-text">
                                    🎮 欢迎来到GameAI智能攻略助手！<br><br>
                                    我是您的专属游戏顾问，拥有丰富的游戏知识库，可以为您提供：<br>
                                    • 🎯 精准的游戏攻略指导<br>
                                    • ⚔️ 专业的角色培养建议<br>
                                    • 🛡️ 详细的装备推荐方案<br>
                                    • 🏆 实用的竞技技巧分享<br><br>
                                    请选择您的游戏类型，然后告诉我您遇到的问题吧！
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="userInput"
                                   placeholder="💬 请输入您的游戏问题，我会为您提供专业解答..."
                                   onkeypress="handleKeyPress(event)"
                                   autocomplete="off">
                            <button class="btn btn-primary" id="sendButton" onclick="sendMessage()">
                                <i class="bi bi-send"></i> 发送
                            </button>
                        </div>

                        <!-- 输入提示 -->
                        <div class="input-hints mt-2">
                            <small class="text-muted">
                                💡 提示：您可以问我关于游戏机制、攻略技巧、装备选择等任何问题
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 知识库加载模态框
    <div class="modal fade" id="knowledgeModal" tabindex="-1" data-bs-backdrop="true" data-bs-keyboard="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">加载知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="datasetId" class="form-label">数据集ID</label>
                        <input type="text" class="form-control" id="datasetId"
                               placeholder="请输入魔搭数据集ID">
                    </div>
                    <div class="mb-3">
                        <label for="subsetName" class="form-label">子集名称（可选）</label>
                        <input type="text" class="form-control" id="subsetName"
                               placeholder="请输入子集名称">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="forceReload">
                        <label class="form-check-label" for="forceReload">
                            强制重新加载
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeModal('knowledgeModal')">取消</button>
                    <button type="button" class="btn btn-primary" onclick="loadKnowledge()">加载</button>
                </div>
            </div>
        </div>
    </div> -->
    

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
