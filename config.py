import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """应用配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    DEBUG = True
    
    # DeepSeek API配置
    DEEPSEEK_API_KEY = "***********************************"
    DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
    DEEPSEEK_MODEL = "deepseek-chat"
    
    # 向量数据库配置
    CHROMA_PERSIST_DIRECTORY = "./data/chroma_db"
    EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
    
    # 知识库配置
    KNOWLEDGE_BASE_PATH = "./data/knowledge_base"
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    
    # 魔搭数据集配置
    MODELSCOPE_DATASET_ID = "your-dataset-id"  # 需要根据实际数据集ID修改
    
    # 游戏助手配置
    MAX_CONTEXT_LENGTH = 4000
    TEMPERATURE = 0.7
    MAX_TOKENS = 1000

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
