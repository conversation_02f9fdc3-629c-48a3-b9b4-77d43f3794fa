"""
数据加载工具模块
"""
import os
import json
import pandas as pd
from typing import List, Dict, Any, Optional
from modelscope.msdatasets import MsDataset
from langchain.text_splitter import RecursiveCharacterTextSplitter
from config import Config

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        """初始化数据加载器"""
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=Config.CHUNK_SIZE,
            chunk_overlap=Config.CHUNK_OVERLAP,
            length_function=len,
            separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
        )
    
    def load_from_modelscope(
        self,
        dataset_id: str,
        subset_name: str = None,
        split: str = 'train'
    ) -> List[Dict[str, Any]]:
        """
        从魔搭平台加载数据集

        Args:
            dataset_id: 数据集ID
            subset_name: 子集名称
            split: 数据分割（train/test/validation）

        Returns:
            加载的数据列表
        """
        try:
            print(f"正在从魔搭平台加载数据集: {dataset_id}")

            # 尝试多种编码方式加载数据集
            encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'latin1']

            for encoding in encodings_to_try:
                try:
                    print(f"尝试使用 {encoding} 编码加载数据...")

                    # 设置pandas读取CSV时的编码
                    import pandas as pd
                    original_read_csv = pd.read_csv

                    def read_csv_with_encoding(*args, **kwargs):
                        kwargs['encoding'] = encoding
                        kwargs['encoding_errors'] = 'ignore'  # 忽略编码错误
                        return original_read_csv(*args, **kwargs)

                    # 临时替换pandas的read_csv函数
                    pd.read_csv = read_csv_with_encoding

                    try:
                        # 加载数据集
                        if subset_name:
                            ds = MsDataset.load(dataset_id, subset_name=subset_name, split=split)
                        else:
                            ds = MsDataset.load(dataset_id, split=split)

                        # 恢复原始的read_csv函数
                        pd.read_csv = original_read_csv

                        print(f"✅ 使用 {encoding} 编码成功加载数据集")
                        break

                    except Exception as encoding_error:
                        # 恢复原始的read_csv函数
                        pd.read_csv = original_read_csv
                        print(f"❌ {encoding} 编码失败: {encoding_error}")
                        if encoding == encodings_to_try[-1]:  # 最后一个编码也失败了
                            raise encoding_error
                        continue

                except Exception as e:
                    if encoding == encodings_to_try[-1]:
                        raise e
                    continue

            # 转换为列表，处理编码问题
            data_list = []
            for i, item in enumerate(ds):
                try:
                    # 确保所有字符串字段都是UTF-8编码
                    processed_item = {}
                    for key, value in item.items():
                        if isinstance(value, str):
                            # 处理可能的编码问题
                            try:
                                # 清理特殊字符
                                cleaned_value = value.replace('\xa0', ' ').replace('\u3000', ' ')
                                processed_item[key] = cleaned_value
                            except:
                                processed_item[key] = str(value)
                        else:
                            processed_item[key] = value

                    data_list.append(processed_item)

                    # 每处理100条数据显示进度
                    if (i + 1) % 100 == 0:
                        print(f"已处理 {i + 1} 条数据...")

                except Exception as item_error:
                    print(f"处理第 {i} 条数据时出错: {item_error}")
                    continue

            print(f"成功加载 {len(data_list)} 条数据")
            return data_list

        except Exception as e:
            print(f"从魔搭平台加载数据失败: {e}")
            print("建议检查数据集ID是否正确，或者数据集是否存在编码问题")
            return []
    
    def load_from_local_files(self, directory_path: str) -> List[Dict[str, Any]]:
        """
        从本地文件加载数据
        
        Args:
            directory_path: 目录路径
            
        Returns:
            加载的数据列表
        """
        data_list = []
        
        try:
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()
                    
                    if file_ext == '.txt':
                        data_list.extend(self._load_txt_file(file_path))
                    elif file_ext == '.json':
                        data_list.extend(self._load_json_file(file_path))
                    elif file_ext == '.csv':
                        data_list.extend(self._load_csv_file(file_path))
            
            print(f"从本地文件加载了 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            print(f"从本地文件加载数据失败: {e}")
            return []
    
    def _load_txt_file(self, file_path: str) -> List[Dict[str, Any]]:
        """加载TXT文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分割文本
            chunks = self.text_splitter.split_text(content)
            
            return [{
                'content': chunk,
                'source': file_path,
                'type': 'text'
            } for chunk in chunks]
            
        except Exception as e:
            print(f"加载TXT文件失败 {file_path}: {e}")
            return []
    
    def _load_json_file(self, file_path: str) -> List[Dict[str, Any]]:
        """加载JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                return data
            else:
                return [data]
                
        except Exception as e:
            print(f"加载JSON文件失败 {file_path}: {e}")
            return []
    
    def _load_csv_file(self, file_path: str) -> List[Dict[str, Any]]:
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)
            return df.to_dict('records')
            
        except Exception as e:
            print(f"加载CSV文件失败 {file_path}: {e}")
            return []
    
    def process_game_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理游戏数据，提取和格式化内容
        
        Args:
            raw_data: 原始数据列表
            
        Returns:
            处理后的数据列表
        """
        processed_data = []
        
        for item in raw_data:
            try:
                # 根据数据结构提取内容
                content = ""
                metadata = {}
                
                # 常见的内容字段名
                content_fields = ['content', 'text', 'description', 'guide', 'strategy', 'answer']
                for field in content_fields:
                    if field in item and item[field]:
                        content = str(item[field])
                        break
                
                if not content:
                    # 如果没有找到内容字段，将整个item转为字符串
                    content = str(item)
                
                # 提取元数据
                for key, value in item.items():
                    if key not in content_fields:
                        metadata[key] = value
                
                # 分割长文本
                if len(content) > Config.CHUNK_SIZE:
                    chunks = self.text_splitter.split_text(content)
                    for i, chunk in enumerate(chunks):
                        chunk_metadata = metadata.copy()
                        chunk_metadata['chunk_index'] = i
                        processed_data.append({
                            'content': chunk,
                            'metadata': chunk_metadata
                        })
                else:
                    processed_data.append({
                        'content': content,
                        'metadata': metadata
                    })
                    
            except Exception as e:
                print(f"处理数据项失败: {e}")
                continue
        
        print(f"处理完成，共 {len(processed_data)} 个数据块")
        return processed_data
    
    def save_processed_data(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """
        保存处理后的数据
        
        Args:
            data: 要保存的数据
            file_path: 保存路径
            
        Returns:
            是否成功保存
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"数据已保存到: {file_path}")
            return True
            
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False

# 全局数据加载器实例
data_loader = DataLoader()
