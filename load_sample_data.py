"""
示例数据加载脚本
用于演示如何加载知识库数据
"""
import os
import sys
import json
from services.knowledge_base import knowledge_base_service

def create_sample_data():
    """创建示例游戏攻略数据"""
    sample_data = [
        {
            "content": "《原神》新手入门指南：首先完成主线任务获得基础角色和装备，然后探索蒙德城周围收集材料。建议优先升级主角和安柏，学会元素反应的基本搭配。",
            "metadata": {
                "game": "原神",
                "type": "新手指南",
                "category": "RPG"
            }
        },
        {
            "content": "《王者荣耀》射手位攻略：射手需要优先发育，前期避免参与团战，专注补兵和推塔。装备推荐：攻速鞋、无尽战刃、影刃、破晓。团战时站位要靠后，输出要稳定。",
            "metadata": {
                "game": "王者荣耀",
                "type": "位置攻略",
                "category": "MOBA"
            }
        },
        {
            "content": "《我的世界》建筑技巧：使用不同材质搭配可以让建筑更有层次感。石砖配橡木原木是经典搭配，玻璃可以增加现代感。建议先画设计图再开始建造。",
            "metadata": {
                "game": "我的世界",
                "type": "建筑攻略",
                "category": "沙盒"
            }
        },
        {
            "content": "《英雄联盟》打野攻略：前期刷野路线很重要，推荐红buff开始，然后小鸟、狼、蓝buff、蛤蟆、石甲虫。注意观察线上情况，适时gank。",
            "metadata": {
                "game": "英雄联盟",
                "type": "位置攻略",
                "category": "MOBA"
            }
        },
        {
            "content": "《和平精英》枪法练习：建议在训练场多练习压枪，掌握不同枪械的后坐力规律。AKM需要下拉较多，M416相对稳定。实战中要学会预瞄和卡点。",
            "metadata": {
                "game": "和平精英",
                "type": "技巧攻略",
                "category": "射击"
            }
        },
        {
            "content": "《炉石传说》卡组构筑：新手推荐使用法师职业，法术较多容易上手。核心卡牌包括火球术、冰箭、奥术飞弹等。构筑时注意法力值曲线的平衡。",
            "metadata": {
                "game": "炉石传说",
                "type": "卡组攻略",
                "category": "卡牌"
            }
        },
        {
            "content": "《文明6》科技发展：前期优先研究畜牧业和采矿业，确保基础资源供应。中期重点发展教育和工程学，为后期的科技爆发做准备。",
            "metadata": {
                "game": "文明6",
                "type": "发展攻略",
                "category": "策略"
            }
        },
        {
            "content": "《模拟人生4》建房技巧：使用moveobjects on作弊码可以自由放置物品。合理利用半墙和栅栏可以创造独特的空间分割效果。",
            "metadata": {
                "game": "模拟人生4",
                "type": "建造攻略",
                "category": "模拟"
            }
        }
    ]
    
    return sample_data

def load_sample_knowledge():
    """加载示例知识到知识库"""
    print("正在创建示例游戏攻略数据...")
    
    # 创建示例数据
    sample_data = create_sample_data()
    
    # 保存到本地文件
    sample_file = os.path.join("data", "knowledge_base", "sample_game_guides.json")
    os.makedirs(os.path.dirname(sample_file), exist_ok=True)
    
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print(f"示例数据已保存到: {sample_file}")
    
    # 处理数据并添加到向量数据库
    print("正在处理数据并添加到向量数据库...")
    
    documents = []
    metadatas = []
    ids = []
    
    for i, item in enumerate(sample_data):
        documents.append(item['content'])
        metadatas.append(item['metadata'])
        ids.append(f"sample_{i}")
    
    # 添加到向量数据库
    success = knowledge_base_service.vector_store.add_documents(
        documents=documents,
        metadatas=metadatas,
        ids=ids
    )
    
    if success:
        print(f"成功添加 {len(documents)} 条示例数据到知识库")
        
        # 显示统计信息
        stats = knowledge_base_service.get_knowledge_stats()
        print(f"知识库统计信息: {stats}")
        
        return True
    else:
        print("添加示例数据失败")
        return False

def test_search():
    """测试搜索功能"""
    print("\n正在测试搜索功能...")
    
    test_queries = [
        "原神新手怎么玩",
        "王者荣耀射手攻略",
        "我的世界建筑技巧",
        "英雄联盟打野路线",
        "枪法练习方法"
    ]
    
    for query in test_queries:
        print(f"\n搜索: {query}")
        results = knowledge_base_service.search_knowledge(query, top_k=2)
        
        for i, result in enumerate(results, 1):
            print(f"  结果{i}: {result['document'][:100]}...")
            print(f"  相似度: {result['similarity']:.3f}")
            print(f"  来源: {result['metadata']}")

def main():
    """主函数"""
    print("=== 游戏攻略助手 - 示例数据加载器 ===\n")
    
    try:
        # 加载示例知识
        success = load_sample_knowledge()
        
        if success:
            # 测试搜索功能
            test_search()
            
            print("\n=== 加载完成 ===")
            print("现在可以启动Flask应用并开始使用游戏攻略助手了！")
            print("运行命令: python app.py")
        else:
            print("示例数据加载失败")
            
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
