/* 游戏攻略助手样式文件 */

/* 导入Google字体 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 全局变量 */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    --accent-gradient: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --success-color: #10ac84;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-radius: 15px;
    --shadow-light: 0 4px 15px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 动态背景 */
body {
    font-family: 'Rajdhani', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

/* 背景动画 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 粒子背景效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -1;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100vh;
    margin: 0;
}

/* 侧边栏样式 */
.sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 25px;
    height: 100vh;
    overflow-y: auto;
    box-shadow: var(--shadow-medium);
    position: relative;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.1;
    z-index: -1;
}

.sidebar-header {
    text-align: center;
    margin-bottom: 35px;
    padding: 25px 15px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    color: white;
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.sidebar-header h4 {
    margin: 0;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.4rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.sidebar-header i {
    font-size: 1.8rem;
    margin-right: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.sidebar .form-label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 游戏类型选择器 */
.sidebar .form-select {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-weight: 500;
    padding: 12px 15px;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.sidebar .form-select:focus {
    background: white;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    outline: none;
    transform: translateY(-2px);
}

.sidebar .form-select option {
    background-color: white;
    color: var(--text-primary);
    padding: 10px;
}

.sidebar h6 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 15px;
    margin-top: 25px;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.sidebar h6::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 3px;
    background: var(--secondary-gradient);
    border-radius: 2px;
}

/* 建议问题样式 */
.suggestions-list {
    max-height: 350px;
    overflow-y: auto;
    padding-right: 5px;
}

.suggestions-list::-webkit-scrollbar {
    width: 6px;
}

.suggestions-list::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}

.suggestions-list::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 3px;
}

.suggestion-item {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.suggestion-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--secondary-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.suggestion-item:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-medium);
    border-color: #667eea;
    color: white;
}

.suggestion-item:hover::before {
    left: 0;
    opacity: 1;
}

.suggestion-item:active {
    transform: translateY(-1px) scale(0.98);
}

/* 主内容区域 */
.main-content {
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.chat-header {
    background: var(--secondary-gradient);
    color: white;
    padding: 25px;
    text-align: center;
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: headerShine 4s infinite;
}

@keyframes headerShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.chat-header h5 {
    margin: 0;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.chat-header i {
    font-size: 1.8rem;
    margin-right: 12px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
    background: transparent;
    position: relative;
}

.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

.message {
    margin-bottom: 25px;
    animation: messageSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes messageSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.user-message .message-content {
    flex-direction: row-reverse;
}

.message-text {
    max-width: 75%;
    padding: 20px 25px;
    border-radius: 25px;
    line-height: 1.7;
    word-wrap: break-word;
    white-space: pre-line;
    font-size: 0.95rem;
    position: relative;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.message-text:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.message-text strong {
    font-weight: 700;
    color: inherit;
}

.assistant-message .message-text strong {
    color: #667eea;
}

.message-text br {
    margin: 10px 0;
}

.user-message .message-text {
    background: var(--primary-gradient);
    color: white;
    border-bottom-right-radius: 8px;
    position: relative;
    overflow: hidden;
}

.user-message .message-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
    pointer-events: none;
}

.assistant-message .message-text {
    background: rgba(255, 255, 255, 0.95);
    color: var(--text-primary);
    border: 2px solid rgba(102, 126, 234, 0.1);
    border-bottom-left-radius: 8px;
    backdrop-filter: blur(10px);
    position: relative;
}

.assistant-message .message-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--secondary-gradient);
    border-radius: 2px;
}

/* 消息图标样式 */
.message i {
    font-size: 1.8rem;
    margin-top: 5px;
    padding: 12px;
    border-radius: 50%;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.user-message i {
    background: var(--primary-gradient);
    color: white;
    animation: userIconPulse 2s infinite;
}

.assistant-message i {
    background: var(--secondary-gradient);
    color: white;
    animation: assistantIconSpin 3s linear infinite;
}

@keyframes userIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes assistantIconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 相关来源样式 */
.sources-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    position: relative;
}

.sources-section::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--secondary-gradient);
}

.sources-title {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sources-title::before {
    content: '📚';
    font-size: 1.2rem;
}

.source-item {
    background: rgba(255, 255, 255, 0.9);
    border-left: 4px solid transparent;
    border-image: var(--secondary-gradient) 1;
    padding: 15px 18px;
    margin-bottom: 12px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    font-size: 0.9rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.source-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--secondary-gradient);
}

.source-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-medium);
    background: white;
}

.source-meta {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
}

.source-meta::before {
    content: '🎯';
    font-size: 0.9rem;
}

/* 输入区域 */
.chat-input {
    padding: 25px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 2px solid rgba(102, 126, 234, 0.2);
    box-shadow: var(--shadow-medium);
    position: relative;
}

.chat-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--secondary-gradient);
}

.chat-input .input-group {
    position: relative;
    overflow: hidden;
    border-radius: 30px;
    box-shadow: var(--shadow-light);
}

.chat-input .form-control {
    border: none;
    border-radius: 30px 0 0 30px;
    padding: 18px 25px;
    font-size: 1rem;
    font-weight: 500;
    background: white;
    color: var(--text-primary);
    transition: var(--transition);
}

.chat-input .form-control:focus {
    outline: none;
    box-shadow: none;
    background: #f8f9ff;
}

.chat-input .form-control::placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

.chat-input .btn {
    border: none;
    border-radius: 0 30px 30px 0;
    padding: 18px 30px;
    font-weight: 700;
    background: var(--secondary-gradient);
    color: white;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.chat-input .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.chat-input .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.chat-input .btn:hover::before {
    left: 100%;
}

.chat-input .btn:active {
    transform: translateY(0);
}

.chat-input .btn i {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* 功能按钮样式 */
.sidebar .btn {
    border: none;
    border-radius: var(--border-radius);
    padding: 12px 20px;
    font-weight: 600;
    margin: 5px 0;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    width: 100%;
    text-align: left;
}

.sidebar .btn-outline-warning {
    background: rgba(255, 255, 255, 0.9);
    color: var(--warning-color);
    border: 2px solid var(--warning-color);
}

.sidebar .btn-outline-warning:hover {
    background: var(--warning-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.sidebar .btn i {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes modernSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

/* 打字机效果 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-gradient);
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingBounce {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1.2); opacity: 1; }
}

/* 通用动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 状态指示器 */
.header-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 0.85rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: statusPulse 2s infinite;
}

.status-indicator.online {
    background: #10ac84;
    box-shadow: 0 0 10px rgba(16, 172, 132, 0.5);
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 输入提示 */
.input-hints {
    text-align: center;
}

/* 统计信息样式 */
.stats-info {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--shadow-light);
}

.stats-info div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.stats-info span {
    font-weight: 600;
    color: var(--primary-gradient);
}

/* 主题样式 */
.theme-dark {
    --primary-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --secondary-gradient: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
}

.theme-gaming {
    --primary-gradient: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    --secondary-gradient: linear-gradient(90deg, #48dbfb 0%, #ff9ff3 100%);
}

.theme-neon {
    --primary-gradient: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
    --secondary-gradient: linear-gradient(90deg, #ff0080 0%, #ff8c00 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .message-text {
        max-width: 85%;
    }
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.btn-close {
    filter: invert(1);
}

/* 确保模态框遮罩层正确显示和隐藏 */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1050 !important;
}

/* 防止模态框遮罩层残留 */
body.modal-open {
    overflow: hidden;
}

/* 加载状态样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.loading-spinner .spinner-border {
    color: #667eea;
}


