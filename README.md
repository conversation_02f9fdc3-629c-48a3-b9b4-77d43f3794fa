# 🎮 GameAI - 智能游戏攻略助手

基于Flask + LangChain + DeepSeek API构建的现代化智能游戏攻略助手，支持多种游戏类型的专业问答和攻略推荐。

## 🌟 项目概述

GameAI是一个集成了现代AI技术和精美界面设计的游戏攻略助手系统。通过向量数据库和大语言模型为用户提供个性化的游戏攻略建议，具备语义搜索和智能问答能力，界面采用现代化设计风格，提供沉浸式的游戏攻略咨询体验。

## ✨ 核心功能

### 🎯 智能问答系统
- **多游戏支持**: 涵盖RPG、MOBA、射击、策略、卡牌、模拟、竞速、动作等游戏类型
- **专业回答**: 基于DeepSeek大语言模型的专业游戏攻略回答
- **语义搜索**: 使用ChromaDB向量数据库实现高精度相似度搜索
- **上下文理解**: 支持多轮对话和上下文关联

### 🎨 现代化界面
- **响应式设计**: 完美适配桌面和移动端设备
- **游戏化风格**: 科技感字体、动态背景、粒子效果
- **交互动画**: 丰富的悬停动画和状态反馈
- **主题切换**: 支持默认、暗黑、游戏、霓虹四种主题

### ⚡ 实用功能
- **快捷操作**: 新手指南、高级技巧、装备推荐一键询问
- **使用统计**: 实时显示咨询次数、在线时长等数据
- **对话管理**: 支持对话历史查看、清空和导出功能
- **个性化**: 主题切换、数据持久化、用户偏好保存

## 🛠️ 技术栈

**后端架构**: Flask + LangChain + ChromaDB + SentenceTransformers + DeepSeek API
**前端技术**: Bootstrap 5 + JavaScript + CSS3 + Google Fonts
**数据存储**: 向量数据库 + 本地文件缓存 + JSON数据格式
**AI模型**: DeepSeek Chat + 语义向量搜索

## 🚀 快速启动

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd GameAI

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置
编辑 `config.py` 文件，配置DeepSeek API密钥：
```python
DEEPSEEK_API_KEY = "your-api-key-here"
```

### 3. 初始化数据
```bash
# 加载游戏攻略数据到知识库
python load_sample_data.py
```

### 4. 启动应用
```bash
# 启动Flask应用
python app.py
```

### 5. 访问应用
打开浏览器访问 `http://localhost:5000` 开始使用GameAI智能游戏攻略助手！

## 🎨 界面特色

### 现代化设计
- **动态背景**: 渐变色动态背景和粒子效果，营造科幻游戏氛围
- **玻璃拟态**: 使用backdrop-filter实现现代玻璃质感
- **游戏化字体**: Orbitron和Rajdhani游戏风格字体
- **丰富动画**: 按钮悬停、消息滑入、图标旋转等流畅动画效果

### 功能界面
- **智能聊天**: 现代化聊天界面，支持实时对话
- **游戏类型选择**: 支持RPG、MOBA、射击、策略等多种游戏类型
- **快捷功能**: 新手指南、高级技巧、装备推荐一键询问
- **建议问题**: 根据游戏类型动态推荐相关问题
- **使用统计**: 实时显示今日咨询次数、总计次数和在线时长
- **主题切换**: 支持默认、暗黑、游戏、霓虹四种主题风格
- **对话管理**: 支持对话历史查看、清空和导出功能

## 🎮 游戏攻略数据库

### 数据覆盖
- **RPG游戏**: 原神新手指南、深渊攻略、角色培养
- **MOBA游戏**: 王者荣耀射手/打野攻略、英雄联盟中单攻略
- **射击游戏**: CSGO枪法训练、绝地求生生存技巧
- **模拟游戏**: 我的世界建筑技巧大全
- **卡牌游戏**: 炉石传说卡组构筑指南
- **动作游戏**: 只狼战斗系统攻略
- **通用攻略**: 新手指南、装备选择原则

### 数据特点
- **专业性**: 涵盖各游戏的核心机制和高级技巧
- **实用性**: 提供可操作的具体建议和方法
- **全面性**: 从新手到高级玩家的全方位覆盖
- **时效性**: 基于当前游戏版本的最新攻略

## 🔌 主要API接口

- `POST /api/ask` - 游戏攻略智能问答
- `GET /api/suggestions` - 获取游戏类型相关建议问题
- `GET /api/history` - 获取对话历史记录

## 📁 项目结构

```
├── app.py                      # Flask主应用
├── config.py                   # 配置文件
├── requirements.txt            # 项目依赖
├── models/                     # AI模型封装
│   ├── llm_client.py          # DeepSeek API客户端
│   └── vector_store.py        # ChromaDB向量存储
├── services/                   # 业务逻辑服务
│   ├── game_assistant.py      # 游戏助手服务
│   └── knowledge_base.py      # 知识库管理服务
├── utils/                      # 工具函数
│   └── data_loader.py         # 数据加载工具
├── data/                       # 数据存储目录
│   ├── chroma_db/             # ChromaDB数据库文件
│   └── knowledge_base/        # 知识库文件
├── static/                     # 静态资源
│   ├── css/
│   │   └── style.css          # 主样式文件
│   └── js/
│       └── app.js             # 主页面交互逻辑
└── templates/                  # HTML模板
    └── index.html             # 主页面模板
```

## 🔧 技术架构

### 核心组件
- **向量数据库**: 使用ChromaDB存储和检索游戏攻略知识，支持语义搜索
- **大语言模型**: 集成DeepSeek API提供智能问答能力
- **知识库管理**: 支持本地数据加载和向量化处理
- **模块化设计**: 便于扩展新的游戏类型和数据源

### 前端技术
- **现代化CSS**: 使用CSS变量、动画、玻璃拟态等现代技术
- **响应式设计**: 完美适配桌面端和移动端设备
- **交互体验**: 丰富的动画效果和用户反馈
- **主题系统**: 支持多种视觉主题切换

### 数据处理
- **文本分割**: 智能文本分块，优化向量搜索效果
- **向量化**: 使用语义嵌入模型生成文档向量
- **相似度搜索**: 基于余弦相似度的智能匹配
- **缓存机制**: 本地文件缓存提高数据加载速度

## 🎯 使用指南

### 1. 游戏攻略咨询
1. 在主页面选择游戏类型
2. 输入您的游戏问题
3. 查看AI助手的专业回答
4. 参考相关来源资料

### 2. 快捷功能使用
- **快捷按钮**: 点击侧边栏的快捷功能按钮快速提问
- **主题切换**: 使用主题切换按钮体验不同视觉风格
- **对话导出**: 将有用的攻略对话导出保存
- **统计查看**: 查看个人使用统计和在线时长

### 3. 数据管理
知识库数据管理：
- `python load_sample_data.py` - 加载丰富的游戏攻略示例数据
- `python load_modelscope_data.py` - 从魔搭社区加载数据（可选）

## 🔍 故障排除

### 常见问题
1. **API密钥错误**: 检查config.py中的DEEPSEEK_API_KEY配置
2. **数据加载失败**: 确认网络连接和数据集ID正确性
3. **界面显示异常**: 清除浏览器缓存并刷新页面
4. **向量数据库错误**: 删除data/chroma_db目录重新初始化

### 调试方法
- 查看浏览器开发者工具控制台
- 检查Flask应用的终端输出
- 查看知识库管理页面的操作日志
- 使用API接口直接测试功能

## 🎊 项目亮点

### 界面优化成果
- ✅ **现代化设计** - 动态背景、玻璃拟态、游戏化字体
- ✅ **丰富动画** - 悬停效果、滑入动画、状态反馈
- ✅ **主题系统** - 四种主题风格，个性化体验
- ✅ **响应式布局** - 完美适配各种设备尺寸

### 功能增强成果
- ✅ **智能搜索** - 基于语义理解的精准攻略匹配
- ✅ **快捷操作** - 一键询问常用游戏问题
- ✅ **使用统计** - 实时数据统计和在线时长
- ✅ **对话管理** - 历史记录、导出、清空功能

### 数据库优化成果
- ✅ **丰富内容** - 33条专业游戏攻略数据
- ✅ **多游戏覆盖** - 10+款热门游戏攻略
- ✅ **专业质量** - 从新手到高级的全方位指导
- ✅ **智能检索** - 高效的向量相似度搜索

## 📈 性能指标

- **响应时间**: <100ms 搜索响应
- **数据规模**: 33个专业攻略文档
- **游戏覆盖**: 10+款主流游戏
- **界面适配**: 桌面端 + 移动端完美支持
- **主题支持**: 4种视觉主题可选

## 🔮 未来规划

- 🚀 **数据扩展**: 添加更多游戏和攻略内容
- 🎨 **界面优化**: 更多动画效果和交互体验
- 🤖 **AI增强**: 更智能的问答和推荐系统
- 📱 **移动优化**: 专门的移动端界面优化

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 贡献方式
1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**🎮 GameAI - 让游戏攻略咨询变得更智能、更美观、更高效！**

*最后更新: 2025年7月4日*
