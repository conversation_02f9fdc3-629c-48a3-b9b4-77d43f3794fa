# 游戏攻略智能助手

基于Flask + LangChain + DeepSeek API构建的智能游戏攻略助手，支持多种游戏类型的智能问答和攻略推荐。

## 🎮 项目概述

这是一个集成了现代AI技术的游戏攻略助手系统，通过向量数据库和大语言模型为用户提供个性化的游戏攻略建议。系统支持从魔搭平台加载知识库，具备语义搜索和智能问答能力。

## ✨ 核心功能

- **多游戏支持**: 涵盖RPG、MOBA、射击、策略等多种游戏类型
- **智能问答**: 基于DeepSeek大语言模型的专业游戏攻略回答
- **语义搜索**: 使用ChromaDB向量数据库实现高精度相似度搜索
- **现代界面**: 响应式Web界面，支持桌面和移动端访问
- **对话管理**: 支持对话历史查看和清空功能

## 🛠️ 技术栈

**后端**: Flask + LangChain + ChromaDB + SentenceTransformers + DeepSeek API
**前端**: Bootstrap 5 + JavaScript + Bootstrap Icons
**数据**: 向量数据库存储 + 本地文件缓存

## 🚀 快速启动

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **配置API密钥**
   编辑 `config.py` 文件中的 `DEEPSEEK_API_KEY`

3. **加载示例数据**
   ```bash
   python load_sample_data.py
   ```

4. **启动应用**
   ```bash
   python app.py
   ```

5. **访问应用**
   - 主页面：`http://localhost:5000`

## 📱 界面功能

### 主页面 (`/`)
- **游戏攻略咨询**: 智能问答聊天界面
- **游戏类型选择**: 支持多种游戏类型切换
- **建议问题**: 根据游戏类型动态推荐问题
- **对话历史管理**: 清空历史对话记录

## 🔌 主要API接口

- `POST /api/ask` - 游戏攻略问答
- `GET /api/suggestions` - 获取建议问题
- `POST /api/clear_history` - 清空对话历史

## 📁 项目结构

```
├── app.py                      # Flask主应用
├── config.py                   # 配置文件
├── requirements.txt            # 项目依赖
├── models/                     # AI模型封装
│   ├── llm_client.py          # DeepSeek API客户端
│   └── vector_store.py        # ChromaDB向量存储
├── services/                   # 业务逻辑服务
│   ├── game_assistant.py      # 游戏助手服务
│   └── knowledge_base.py      # 知识库管理服务
├── utils/                      # 工具函数
│   └── data_loader.py         # 数据加载工具
├── data/                       # 数据存储目录
│   ├── chroma_db/             # ChromaDB数据库文件
│   └── knowledge_base/        # 知识库文件
├── static/                     # 静态资源
│   ├── css/
│   │   └── style.css          # 主样式文件
│   └── js/
│       └── app.js             # 主页面交互逻辑
└── templates/                  # HTML模板
    └── index.html             # 主页面模板
```

## 🔧 开发说明

### 核心架构
- **向量数据库**: 使用ChromaDB存储和检索游戏攻略知识
- **大语言模型**: 集成DeepSeek API提供智能问答能力
- **数据源**: 支持从魔搭平台动态加载游戏数据集
- **模块化设计**: 便于扩展新的游戏类型和数据源

### 界面特色
- **响应式设计**: 完美适配桌面端和移动端设备
- **现代化UI**: 渐变色彩和流畅动画效果
- **用户友好**: 简洁直观的操作界面

## 🎯 使用指南

### 1. 游戏攻略咨询
1. 在主页面选择游戏类型
2. 输入您的游戏问题
3. 查看AI助手的专业回答
4. 参考相关来源资料

### 2. 数据集管理
知识库数据可以通过以下脚本进行管理：
- `python load_sample_data.py` - 加载示例数据
- `python load_modelscope_data.py` - 从魔搭社区加载数据
- `python add_manual_knowledge.py` - 手动添加知识

## 🔍 故障排除

### 常见问题
1. **API密钥错误**: 检查config.py中的DEEPSEEK_API_KEY配置
2. **数据加载失败**: 确认网络连接和数据集ID正确性
3. **界面显示异常**: 清除浏览器缓存并刷新页面
4. **向量数据库错误**: 删除data/chroma_db目录重新初始化

### 调试方法
- 查看浏览器开发者工具控制台
- 检查Flask应用的终端输出
- 查看知识库管理页面的操作日志
- 使用API接口直接测试功能

## 📈 性能优化

- **向量检索**: 使用高效的相似度搜索算法
- **缓存机制**: 本地缓存常用数据和模型
- **异步处理**: 非阻塞的数据加载和处理
- **资源压缩**: 优化静态资源加载速度

---

*基于现代AI技术构建的专业游戏攻略助手，为游戏玩家提供智能化的攻略建议和技巧指导。*
