"""
向量数据库管理模块
"""
import os
import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional
from config import Config
import numpy as np
import hashlib

class VectorStore:
    """向量数据库管理类"""
    
    def __init__(self, persist_directory: str = None, collection_name: str = "game_knowledge"):
        """
        初始化向量数据库
        
        Args:
            persist_directory: 持久化目录
            collection_name: 集合名称
        """
        self.persist_directory = persist_directory or Config.CHROMA_PERSIST_DIRECTORY
        self.collection_name = collection_name
        
        # 确保目录存在
        os.makedirs(self.persist_directory, exist_ok=True)
        
        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(path=self.persist_directory)

        # 获取或创建集合
        try:
            self.collection = self.client.get_collection(name=self.collection_name)
        except:
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "游戏攻略知识库"}
            )
    
    def _simple_embedding(self, text: str) -> List[float]:
        """
        简单的文本嵌入方法（使用TF-IDF风格的向量化）
        这是一个临时解决方案，生产环境建议使用专业的嵌入模型
        """
        # 简单的字符级别特征提取
        text = text.lower()
        # 创建一个固定长度的向量
        vector_size = 384  # 模拟sentence-transformers的输出维度
        vector = [0.0] * vector_size

        # 基于文本内容生成特征
        for i, char in enumerate(text[:vector_size]):
            vector[i] = ord(char) / 255.0  # 归一化到0-1

        # 添加一些基于文本统计的特征
        text_hash = int(hashlib.md5(text.encode()).hexdigest()[:8], 16)
        for i in range(min(50, vector_size)):
            vector[i] += (text_hash >> i) & 1

        # 归一化向量
        norm = sum(x*x for x in vector) ** 0.5
        if norm > 0:
            vector = [x/norm for x in vector]

        return vector

    def add_documents(
        self,
        documents: List[str],
        metadatas: List[Dict[str, Any]] = None,
        ids: List[str] = None
    ) -> bool:
        """
        添加文档到向量数据库

        Args:
            documents: 文档列表
            metadatas: 元数据列表
            ids: 文档ID列表

        Returns:
            是否成功添加
        """
        try:
            # 生成嵌入向量
            embeddings = [self._simple_embedding(doc) for doc in documents]

            # 生成ID（如果未提供）
            if ids is None:
                ids = [f"doc_{i}" for i in range(len(documents))]

            # 生成默认元数据（如果未提供）
            if metadatas is None:
                metadatas = [{"source": "unknown"} for _ in documents]

            # 添加到集合
            self.collection.add(
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )

            print(f"成功添加 {len(documents)} 个文档到向量数据库")
            return True

        except Exception as e:
            print(f"添加文档到向量数据库失败: {e}")
            return False
    
    def search_similar(
        self, 
        query: str, 
        n_results: int = 5,
        where: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索相似文档
        
        Args:
            query: 查询文本
            n_results: 返回结果数量
            where: 过滤条件
            
        Returns:
            相似文档列表
        """
        try:
            # 生成查询嵌入
            query_embedding = [self._simple_embedding(query)]

            # 搜索相似文档
            results = self.collection.query(
                query_embeddings=query_embedding,
                n_results=n_results,
                where=where,
                include=["documents", "metadatas", "distances"]
            )
            
            # 格式化结果
            formatted_results = []
            for i in range(len(results['documents'][0])):
                distance = results['distances'][0][i]
                # 修复相似度计算：对于欧几里得距离，相似度应该是距离的倒数
                # 使用更合理的相似度计算方法
                similarity = 1.0 / (1.0 + distance) if distance >= 0 else 0.0

                formatted_results.append({
                    'document': results['documents'][0][i],
                    'metadata': results['metadatas'][0][i],
                    'distance': distance,
                    'similarity': similarity
                })
            
            return formatted_results
            
        except Exception as e:
            print(f"搜索相似文档失败: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息
        
        Returns:
            集合信息字典
        """
        try:
            count = self.collection.count()
            return {
                "name": self.collection_name,
                "count": count,
                "persist_directory": self.persist_directory
            }
        except Exception as e:
            print(f"获取集合信息失败: {e}")
            return {}
    
    def delete_collection(self) -> bool:
        """
        删除集合
        
        Returns:
            是否成功删除
        """
        try:
            self.client.delete_collection(name=self.collection_name)
            print(f"成功删除集合: {self.collection_name}")
            return True
        except Exception as e:
            print(f"删除集合失败: {e}")
            return False
    
    def update_document(self, doc_id: str, document: str, metadata: Dict[str, Any] = None) -> bool:
        """
        更新文档
        
        Args:
            doc_id: 文档ID
            document: 新文档内容
            metadata: 新元数据
            
        Returns:
            是否成功更新
        """
        try:
            # 生成新的嵌入向量
            embedding = self._simple_embedding(document)

            # 更新文档
            self.collection.update(
                ids=[doc_id],
                embeddings=[embedding],
                documents=[document],
                metadatas=[metadata] if metadata else None
            )
            
            print(f"成功更新文档: {doc_id}")
            return True
            
        except Exception as e:
            print(f"更新文档失败: {e}")
            return False

# 全局向量存储实例
vector_store = VectorStore()
