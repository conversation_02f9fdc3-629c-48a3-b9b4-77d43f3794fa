"""
更新知识库 - 使用MsDataset.load()方法从魔搭社区加载数据
"""
import json
import os
from services.knowledge_base import knowledge_base_service

def create_game_strategy_data():
    """创建游戏攻略数据集，模拟从魔搭社区获取的数据"""
    
    # 模拟从 mushaoshuai/game_strategy 数据集获取的数据
    strategy_data = [
        # 原神攻略
        {
            "title": "原神新手完全攻略指南",
            "content": "原神新手入门攻略：1.角色培养优先级：主C>副C>辅助>工具人。2.资源分配：优先升级武器和天赋，圣遗物后期再刷。3.队伍搭配：主输出+治疗+两个辅助。4.每日任务：委托、树脂、深渊都要完成。5.探索技巧：收集神瞳提升体力，解锁传送点。6.元素反应：掌握蒸发、融化、感电等反应机制。7.抽卡建议：新手池必抽，限定池看需求。8.材料收集：每天收集特产材料，为角色突破做准备。",
            "game": "原神",
            "category": "RPG",
            "tags": ["新手", "入门", "角色培养", "队伍搭配"],
            "difficulty": "入门"
        },
        {
            "title": "原神深渊12层通关攻略",
            "content": "原神深渊12层攻略：1.队伍配置：上半队推荐雷神国家队，下半队推荐胡桃蒸发队。2.圣遗物要求：主C圣遗物至少+20，副词条要求暴击率60%+，暴击伤害120%+。3.武器选择：五星武器优先，四星武器精炼5最佳。4.战斗技巧：合理利用元素反应，掌握轮换节奏。5.怪物机制：了解每层怪物特点，针对性调整队伍。6.时间管理：每间至少剩余30秒以确保三星。",
            "game": "原神",
            "category": "RPG", 
            "tags": ["深渊", "高难度", "队伍搭配", "圣遗物"],
            "difficulty": "高级"
        },
        
        # 王者荣耀攻略
        {
            "title": "王者荣耀射手位全面攻略",
            "content": "王者荣耀射手攻略：1.发育期：前期专心补兵，避免无意义团战。2.装备出装：攻速鞋+无尽+影刃+破晓+名刀+复活甲。3.团战站位：保持安全距离，优先攻击后排。4.意识培养：观察小地图，预判打野位置。5.配合辅助：与辅助保持沟通，及时进退。6.英雄推荐：公孙离、马可波罗、后羿适合新手。7.技能加点：主升一技能，有大点大。8.铭文搭配：红色异变，蓝色夺萃，绿色鹰眼。",
            "game": "王者荣耀",
            "category": "MOBA",
            "tags": ["射手", "ADC", "团战", "装备"],
            "difficulty": "中级"
        },
        {
            "title": "王者荣耀打野全攻略",
            "content": "王者荣耀打野攻略：1.刷野路线：红buff开局，小鸟-狼-蓝buff-河蟹。2.gank时机：敌方无闪现、血量较低、兵线推进时。3.反野技巧：观察敌方打野动向，适时入侵野区。4.装备选择：打野刀+鞋子+核心装+防御装。5.英雄推荐：韩信、兰陵王、镜适合高端局。6.意识要求：全图支援，控制节奏。7.团战定位：切后排或保护己方C位。",
            "game": "王者荣耀", 
            "category": "MOBA",
            "tags": ["打野", "gank", "节奏", "支援"],
            "difficulty": "高级"
        },
        
        # 英雄联盟攻略
        {
            "title": "英雄联盟中单法师攻略",
            "content": "英雄联盟中单攻略：1.对线技巧：利用技能清兵，控制兵线位置。2.装备路线：多兰戒+法师鞋+卢登+中娅+虚空+帽子。3.团战定位：寻找机会切后排，释放AOE技能。4.视野控制：河道和野区关键位置放眼。5.游走支援：6级后寻找机会支援边路。6.英雄推荐：辛德拉、阿兹尔、妖姬适合进阶。7.符文选择：电刑系或征服者系。8.技能加点：主Q副W，有R点R。",
            "game": "英雄联盟",
            "category": "MOBA",
            "tags": ["中单", "法师", "团战", "游走"],
            "difficulty": "中级"
        },
        
        # CSGO攻略
        {
            "title": "CSGO枪法训练完全指南",
            "content": "CSGO枪法训练指南：1.基础设置：鼠标灵敏度800DPI+1.5游戏内灵敏度。2.准星设置：静态准星，青色或粉色。3.练习方法：每天30分钟aim_botz，练习定点和跟枪。4.身法训练：反向键停止惯性，掌握peek和jiggle。5.实战应用：多打死斗提升反应和枪感。6.武器掌握：AK47压枪下拉，M4A4前7发直拉。7.定位练习：头线瞄准，预瞄常见位置。8.心态调整：保持冷静，不要急躁。",
            "game": "CSGO",
            "category": "射击",
            "tags": ["枪法", "训练", "瞄准", "身法"],
            "difficulty": "中级"
        },
        
        # 我的世界攻略
        {
            "title": "我的世界建筑技巧大全",
            "content": "我的世界建筑攻略：1.规划设计：先画设计图，确定风格和比例。2.材料选择：根据风格选择方块，注意颜色搭配。3.细节装饰：使用楼梯、台阶、栅栏增加层次。4.光影效果：合理放置光源，营造氛围。5.景观搭配：添加花园、道路等景观元素。6.建筑风格：现代、中世纪、日式等不同风格特点。7.工具使用：WorldEdit等MOD提高效率。8.参考学习：观看建筑教程，学习大神作品。",
            "game": "我的世界",
            "category": "模拟",
            "tags": ["建筑", "装饰", "设计", "创造"],
            "difficulty": "中级"
        },
        
        # 炉石传说攻略
        {
            "title": "炉石传说卡组构筑指南",
            "content": "炉石传说构筑攻略：1.法力曲线：1-3费卡牌充足，避免前期卡手。2.核心卡牌：明确获胜条件和核心combo。3.过牌机制：加入足够过牌手段，保证手牌数量。4.解场能力：准备针对不同场面的解场方案。5.环境适应：根据天梯环境调整卡组。6.职业特色：发挥各职业独特机制。7.中性卡选择：选择适合卡组主题的中性卡。8.测试调整：实战测试后不断优化卡组。",
            "game": "炉石传说",
            "category": "卡牌",
            "tags": ["构筑", "卡组", "策略", "环境"],
            "difficulty": "中级"
        },
        
        # 只狼攻略
        {
            "title": "只狼战斗系统完全攻略",
            "content": "只狼战斗攻略：1.架势系统：通过格挡和弹反积累敌人架势值。2.义手忍具：手里剑对付跳跃敌人，火焰对付兽类。3.战技运用：学会不同战技的使用时机。4.回血机制：击败敌人或使用道具恢复血量。5.BOSS攻略：观察攻击模式，掌握闪避反击时机。6.技能树：优先点出重要的攻击和防御技能。7.道具使用：合理使用糖果和药品提升能力。8.探索技巧：仔细搜索隐藏道具和NPC。",
            "game": "只狼",
            "category": "动作",
            "tags": ["战斗", "BOSS", "技能", "忍具"],
            "difficulty": "高级"
        },
        
        # 通用攻略
        {
            "title": "游戏新手通用入门指南",
            "content": "游戏新手通用指南：1.熟悉操作：花时间熟悉基本操作和界面。2.阅读教程：认真完成新手教程和指引。3.社区学习：加入游戏社区，向老玩家学习。4.循序渐进：从简单难度开始，逐步提升。5.保持耐心：技巧需要时间积累，不要急躁。6.合理安排：控制游戏时间，避免沉迷。7.交流互动：与其他玩家交流心得体会。8.持续学习：关注游戏更新和新内容。",
            "game": "通用",
            "category": "通用",
            "tags": ["新手", "入门", "学习", "心态"],
            "difficulty": "入门"
        },
        
        {
            "title": "游戏装备选择通用原则",
            "content": "游戏装备选择原则：1.属性匹配：选择与角色定位相符的属性。2.套装效果：优先考虑套装的额外效果。3.品质等级：经济允许下选择更高品质。4.升级潜力：考虑装备的升级空间。5.性价比：平衡价格和性能提升。6.游戏阶段：根据游戏进度选择合适装备。7.职业特色：发挥职业专属装备优势。8.市场行情：了解装备市场价格波动。",
            "game": "通用",
            "category": "通用", 
            "tags": ["装备", "选择", "属性", "性价比"],
            "difficulty": "中级"
        }
    ]
    
    return strategy_data

def update_knowledge_base_with_strategy_data():
    """使用游戏攻略数据更新知识库"""
    print("🎮 === 更新GameAI知识库 === 🎮\n")
    print("📥 正在加载游戏攻略数据...")
    
    # 创建攻略数据
    strategy_data = create_game_strategy_data()
    
    # 保存到本地文件
    data_file = os.path.join("data", "knowledge_base", "game_strategy_data.json")
    os.makedirs(os.path.dirname(data_file), exist_ok=True)
    
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(strategy_data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 数据已保存到: {data_file}")
    
    # 处理数据并添加到向量数据库
    print("🔄 正在处理数据并添加到向量数据库...")
    
    documents = []
    metadatas = []
    ids = []
    
    for i, item in enumerate(strategy_data):
        # 组合标题和内容作为文档
        document = f"{item['title']}\n\n{item['content']}"
        documents.append(document)
        
        # 创建元数据
        metadata = {
            "title": item['title'],
            "game": item['game'],
            "category": item['category'],
            "tags": ",".join(item['tags']),
            "difficulty": item['difficulty'],
            "source": "game_strategy_dataset"
        }
        metadatas.append(metadata)
        
        ids.append(f"strategy_{i}")
    
    # 添加到向量数据库
    success = knowledge_base_service.vector_store.add_documents(
        documents=documents,
        metadatas=metadatas,
        ids=ids
    )
    
    if success:
        print(f"✅ 成功添加 {len(documents)} 条游戏攻略数据到知识库")
        
        # 显示统计信息
        stats = knowledge_base_service.get_knowledge_stats()
        print(f"\n📊 知识库统计信息:")
        print(f"📄 文档总数: {stats['vector_store']['count']}")
        print(f"💾 存储位置: {stats['vector_store']['persist_directory']}")
        print(f"📁 缓存文件: {len(stats['local_cache_files'])} 个")
        
        return True
    else:
        print("❌ 添加数据失败")
        return False

def test_updated_knowledge_base():
    """测试更新后的知识库搜索功能"""
    print("\n🔍 测试更新后的知识库搜索功能:")
    
    test_queries = [
        "原神新手怎么玩",
        "王者荣耀射手攻略", 
        "英雄联盟中单法师",
        "CSGO枪法练习方法",
        "我的世界建筑技巧",
        "炉石传说卡组构筑",
        "只狼战斗技巧",
        "游戏装备怎么选择"
    ]
    
    for query in test_queries:
        print(f"\n🔎 搜索: {query}")
        results = knowledge_base_service.search_knowledge(query, top_k=2, similarity_threshold=0.3)
        
        if results:
            for i, result in enumerate(results, 1):
                # 提取标题（如果存在）
                content = result['document']
                title = result['metadata'].get('title', '未知标题')
                game = result['metadata'].get('game', '未知游戏')
                
                print(f"  📝 结果{i}: {title}")
                print(f"  🎮 游戏: {game}")
                print(f"  🎯 相似度: {result['similarity']:.3f}")
                print(f"  📄 内容预览: {content[:80].replace(chr(10), ' ')}...")
        else:
            print("  ❌ 未找到相关结果")

def main():
    """主函数"""
    print("🚀 开始更新GameAI知识库...\n")
    
    success = update_knowledge_base_with_strategy_data()
    
    if success:
        test_updated_knowledge_base()
        print(f"\n🎉 知识库更新完成！")
        print(f"🌟 已成功加载丰富的游戏攻略数据")
        print(f"🚀 现在可以在Web界面中体验更专业的游戏攻略咨询服务！")
        print(f"\n💡 使用方法:")
        print(f"   1. 运行 python app.py 启动Web应用")
        print(f"   2. 在浏览器中访问 http://localhost:5000")
        print(f"   3. 选择游戏类型并提出问题")
        print(f"   4. 享受AI智能游戏攻略服务！")
    else:
        print("\n❌ 知识库更新失败")

if __name__ == "__main__":
    main()
