"""
从魔搭社区加载游戏数据集
数据集: mushaoshuai/game_data
"""
import os
import sys
from services.knowledge_base import knowledge_base_service

def load_game_data_from_modelscope():
    """从魔搭社区加载游戏数据集"""
    print("=== 从魔搭社区加载游戏数据集 ===\n")
    
    dataset_id = "mushaoshuai/game_data"
    subset_name = "default"
    split = "train"
    
    print(f"数据集ID: {dataset_id}")
    print(f"子集名称: {subset_name}")
    print(f"数据分割: {split}")
    print()
    
    try:
        # 使用知识库服务加载数据
        print("正在从魔搭社区下载和处理数据...")
        success = knowledge_base_service.load_knowledge_from_modelscope(
            dataset_id=dataset_id,
            subset_name=subset_name
        )
        
        if success:
            print("✅ 数据加载成功！")
            
            # 获取统计信息
            stats = knowledge_base_service.get_knowledge_stats()
            print(f"\n📊 知识库统计信息:")
            print(f"- 文档总数: {stats['vector_store']['count']}")
            print(f"- 存储位置: {stats['vector_store']['persist_directory']}")
            print(f"- 缓存文件: {len(stats['local_cache_files'])} 个")
            
            # 测试搜索功能
            print("\n🔍 测试搜索功能:")
            test_queries = [
                "游戏攻略",
                "新手指南", 
                "装备推荐",
                "技能搭配",
                "副本攻略"
            ]
            
            for query in test_queries:
                print(f"\n搜索: {query}")
                results = knowledge_base_service.search_knowledge(query, top_k=2)
                
                if results:
                    for i, result in enumerate(results, 1):
                        print(f"  结果{i}: {result['document'][:100]}...")
                        print(f"  相似度: {result['similarity']:.3f}")
                else:
                    print("  未找到相关结果")
            
            print("\n🎉 数据加载和测试完成！")
            print("现在您可以在Web界面中使用这些新的游戏数据了。")
            
        else:
            print("❌ 数据加载失败，请检查网络连接和数据集ID")
            
    except Exception as e:
        print(f"❌ 加载过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始加载魔搭社区游戏数据集...\n")
    load_game_data_from_modelscope()

if __name__ == "__main__":
    main()
