import os
import shutil
import traceback
from typing import List, Dict, Any, Optional

# 请确保你的项目中有 services.knowledge_base 模块
from services.knowledge_base import knowledge_base_service

os.environ["MODELSCOPE_DISABLE_AST"] = "1"

try:
    import pandas as pd
except ImportError:
    pd = None

def clear_modelscope_cache():
    cache_dir = os.path.join(os.path.expanduser('~'), '.cache', 'modelscope')
    if os.path.exists(cache_dir):
        print(f"🧹 正在清理 ModelScope 缓存: {cache_dir}")
        shutil.rmtree(cache_dir)
        print("✅ 清理完成")

def load_dataset_with_fallback(dataset_id: str, subset_name: Optional[str] = None) -> Optional[Any]:
    from modelscope.msdatasets import MsDataset

    print(f"🔄 尝试加载数据集: {dataset_id}")

    load_methods = [
        lambda: MsDataset.load(dataset_id),
        lambda: MsDataset.load(dataset_id, subset_name=subset_name) if subset_name else None,
        lambda: MsDataset.load(dataset_id, split='train'),
        lambda: MsDataset.load(dataset_id, subset_name=subset_name, split='train') if subset_name else None,
    ]

    for i, method in enumerate(load_methods, 1):
        if method is None:
            continue
        try:
            print(f"  📥 尝试方法 {i}...")
            dataset = method()
            if dataset is not None:
                print(f"  ✅ 方法 {i} 成功！")
                return dataset
        except UnicodeDecodeError as e:
            print(f"  ⚠️ 编码错误，尝试清理缓存后重新下载: {type(e).__name__}: {e}")
            clear_modelscope_cache()
            continue
        except Exception as e:
            print(f"  ❌ 方法 {i} 失败: {type(e).__name__}: {str(e)[:150]}...")
            continue

    return None

def extract_content_from_dict(item: Dict[str, Any]) -> Optional[str]:
    content_fields = [
        'content', 'text', 'description', 'body', 'message',
        'title', 'question', 'answer', 'strategy', 'guide',
        '内容', '文本', '描述', '标题', '问题', '答案', '攻略', '指南'
    ]
    for field in content_fields:
        if field in item and item[field]:
            content = str(item[field]).strip()
            if len(content) > 10:
                return content

    combined_content = []
    for key, value in item.items():
        if isinstance(value, (str, int, float)) and str(value).strip():
            combined_content.append(f"{key}: {value}")

    if combined_content:
        return "\n".join(combined_content)
    return None

def process_dataset_to_documents(dataset: Any) -> List[Dict[str, Any]]:
    documents = []
    print("🔄 正在处理数据集...")

    data_items = []
    try:
        for item in dataset:
            data_items.append(item)
            if len(data_items) >= 1000:
                break
    except Exception as e:
        print(f"  ❌ 直接迭代失败: {type(e).__name__}: {e}")
        try:
            df = dataset.to_pandas()
            data_items = df.to_dict('records')
        except Exception as e2:
            print(f"  ❌ DataFrame 转换失败: {type(e2).__name__}: {e2}")
            try:
                if hasattr(dataset, 'data'):
                    data_items = list(dataset.data)
                elif hasattr(dataset, '__getitem__'):
                    for i in range(min(1000, len(dataset))):
                        data_items.append(dataset[i])
            except Exception as e3:
                print(f"  ❌ 数据获取失败: {type(e3).__name__}: {e3}")
                return []

    print(f"📊 获取到 {len(data_items)} 条数据")

    for i, item in enumerate(data_items):
        try:
            if isinstance(item, dict):
                content = extract_content_from_dict(item)
                if content:
                    documents.append({
                        'content': content,
                        'metadata': {
                            'source': 'modelscope',
                            'index': i,
                            'original_keys': list(item.keys())
                        }
                    })
            elif isinstance(item, str):
                if len(item.strip()) > 10:
                    documents.append({
                        'content': item.strip(),
                        'metadata': {
                            'source': 'modelscope',
                            'index': i,
                            'type': 'string'
                        }
                    })
            else:
                content = str(item).strip()
                if len(content) > 10:
                    documents.append({
                        'content': content,
                        'metadata': {
                            'source': 'modelscope',
                            'index': i,
                            'type': type(item).__name__
                        }
                    })
        except Exception as e:
            print(f"  ⚠️ 处理第 {i} 条数据时出错: {type(e).__name__}: {e}")
            continue

    print(f"✅ 成功处理 {len(documents)} 条有效文档")
    return documents

def load_game_data_from_modelscope():
    print("🎮 === 从魔搭社区加载游戏数据集 === 🎮\n")

    dataset_candidates = [
        "mushaoshuai/game_data",
        "mushaoshuai/game_strategy",
        "game_data",
        "game_strategy"
    ]

    print("📦 候选数据集:")
    for i, dataset_id in enumerate(dataset_candidates, 1):
        print(f"  {i}. {dataset_id}")
    print()

    try:
        from modelscope.msdatasets import MsDataset
        print("✅ ModelScope 依赖包已安装")

        dataset = None
        successful_dataset_id = None

        for dataset_id in dataset_candidates:
            print(f"\n🔄 尝试加载数据集: {dataset_id}")
            dataset = load_dataset_with_fallback(dataset_id, subset_name="default")
            if dataset is not None:
                successful_dataset_id = dataset_id
                print(f"✅ 成功加载数据集: {dataset_id}")
                break

        if dataset is None:
            print("❌ 所有数据集加载失败")
            return False

        documents = process_dataset_to_documents(dataset)
        if not documents:
            print("❌ 未能从数据集中提取有效文档")
            return False

        print(f"\n📚 正在将 {len(documents)} 条文档添加到知识库...")

        doc_texts = []
        doc_metadatas = []
        doc_ids = []

        for i, doc in enumerate(documents):
            doc_texts.append(doc['content'])
            metadata = doc['metadata'].copy()
            metadata['dataset_id'] = successful_dataset_id
            if pd is not None:
                metadata['processed_at'] = str(pd.Timestamp.now())
            else:
                from datetime import datetime
                metadata['processed_at'] = datetime.now().isoformat()
            doc_metadatas.append(metadata)
            doc_ids.append(f"modelscope_{successful_dataset_id}_{i}")

        success = knowledge_base_service.vector_store.add_documents(
            documents=doc_texts,
            metadatas=doc_metadatas,
            ids=doc_ids
        )

        if success:
            print("\n🎉 数据加载成功！知识库已更新")
            return True
        else:
            print("❌ 数据加载失败")
            return False

    except Exception as e:
        print(f"❌ 加载过程中发生错误: {type(e).__name__}: {e}")
        traceback.print_exc()
        return False

def main():
    print("🎮 开始加载魔搭社区游戏数据集...\n")
    success = load_game_data_from_modelscope()
    if success:
        print("\n✅ 所有操作完成！知识库已更新。")
    else:
        print("\n❌ 数据加载失败，请检查错误信息并重试。")

if __name__ == "__main__":
    main()
