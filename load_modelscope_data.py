"""
从魔搭社区加载游戏数据集
数据集: mushaoshuai/game_data
"""
import os
import sys
from services.knowledge_base import knowledge_base_service

def load_game_data_from_modelscope():
    """从魔搭社区加载游戏数据集"""
    print("🎮 === 从魔搭社区加载游戏数据集 === 🎮\n")

    dataset_id = "mushaoshuai/game_data"
    subset_name = "default"  # 使用默认子集
    split = "train"

    print(f"📦 数据集ID: {dataset_id}")
    print(f"📂 子集名称: {subset_name}")
    print(f"🔄 数据分割: {split}")
    print(f"🌐 数据来源: 魔搭社区 (ModelScope)")
    print()
    
    try:
        # 检查依赖
        print("🔍 检查依赖包...")
        try:
            from modelscope.msdatasets import MsDataset
            print("✅ ModelScope 依赖包已安装")
        except ImportError:
            print("❌ ModelScope 依赖包未安装，请运行: pip install modelscope")
            return False

        # 使用知识库服务加载数据
        print("📥 正在从魔搭社区下载游戏攻略数据...")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")

        success = knowledge_base_service.load_knowledge_from_modelscope(
            dataset_id=dataset_id,
            subset_name=subset_name,
            force_reload=False  # 如果已有缓存则使用缓存
        )

        if success:
            print("\n🎉 数据加载成功！")

            # 获取统计信息
            stats = knowledge_base_service.get_knowledge_stats()
            print(f"\n📊 知识库统计信息:")
            print(f"📄 文档总数: {stats['vector_store']['count']}")
            print(f"💾 存储位置: {stats['vector_store']['persist_directory']}")
            print(f"📁 缓存文件: {len(stats['local_cache_files'])} 个")

            # 测试搜索功能
            print("\n🔍 测试搜索功能:")
            test_queries = [
                "游戏新手攻略",
                "角色培养指南",
                "装备选择推荐",
                "技能搭配方案",
                "副本通关攻略",
                "PVP对战技巧",
                "资源获取方法"
            ]

            for query in test_queries:
                print(f"\n🔎 搜索: {query}")
                results = knowledge_base_service.search_knowledge(query, top_k=2, similarity_threshold=0.5)

                if results:
                    for i, result in enumerate(results, 1):
                        content_preview = result['document'][:120].replace('\n', ' ')
                        print(f"  📝 结果{i}: {content_preview}...")
                        print(f"  🎯 相似度: {result['similarity']:.3f}")
                        print(f"  📂 来源: {result['metadata'].get('source', '未知')}")
                else:
                    print("  ❌ 未找到相关结果")

            print(f"\n🎊 游戏攻略数据加载和测试完成！")
            print(f"🌟 成功加载了丰富的游戏攻略知识库")
            print(f"🚀 现在您可以在Web界面中享受更专业的游戏攻略咨询服务了！")

            return True

        else:
            print("❌ 数据加载失败")
            print("💡 可能的原因:")
            print("   - 网络连接问题")
            print("   - 数据集ID不正确")
            print("   - ModelScope服务暂时不可用")
            print("   - 权限不足")
            return False

    except Exception as e:
        print(f"❌ 加载过程中发生错误: {e}")
        print("\n🔧 错误详情:")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎮 开始加载魔搭社区游戏数据集...\n")
    success = load_game_data_from_modelscope()

    if success:
        print("\n✅ 所有操作完成！知识库已更新。")
    else:
        print("\n❌ 数据加载失败，请检查错误信息并重试。")

if __name__ == "__main__":
    main()
